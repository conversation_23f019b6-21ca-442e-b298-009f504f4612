(()=>{var e={6942:(e,t)=>{var r;!function(){"use strict";var s={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=n(e,o(r)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)s.call(e,r)&&e[r]&&(t=n(t,r));return t}function n(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(r=function(){return a}.apply(t,[]))||(e.exports=r)}()}},t={};function r(s){var a=t[s];if(void 0!==a)return a.exports;var o=t[s]={exports:{}};return e[s](o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blockEditor;var s=r(6942),a=r.n(s);const o=window.wp.data,n=window.wp.element,l=window.wp.i18n,i={};let c;"undefined"!=typeof lpDataAdmin&&(c=lpDataAdmin.lp_rest_url,i.admin={apiAdminNotice:c+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:c+"lp/v1/orders/statistic",apiAddons:c+"lp/v1/addon/all",apiAddonAction:c+"lp/v1/addon/action-n",apiAddonsPurchase:c+"lp/v1/addon/info-addons-purchase",apiSearchCourses:c+"lp/v1/admin/tools/search-course",apiSearchUsers:c+"lp/v1/admin/tools/search-user",apiAssignUserCourse:c+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:c+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(c=lpData.lp_rest_url,i.frontend={apiWidgets:c+"lp/v1/widgets/api",apiCourses:c+"lp/v1/courses/archive-course",apiAJAX:c+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:c+"lp/v1/profile/cover-image"}),c&&(i.apiCourses=c+"lp/v1/courses/");const u=i,p=[["learnpress/course-image"],["learnpress/course-title"],["learnpress/course-price"]];function d({classList:r}){const s=(0,t.useInnerBlocksProps)({className:a()("wp-block-learnpress-course-item-template")},{template:p});return(0,e.createElement)("li",{className:"course"},(0,e.createElement)("div",{...s}))}const m=(0,n.memo)((function({blocks:r,blockContextId:s,classList:a,isHidden:o,setActiveBlockContextId:n}){const l=(0,t.__experimentalUseBlockPreview)({blocks:r}),i=()=>{n(s)},c={display:o?"none":void 0};return(0,e.createElement)("li",{className:"course",tabIndex:0,role:"button",onClick:i,onKeyPress:i,style:c},(0,e.createElement)("div",{...l,className:"wp-block-learnpress-course-item-template"}))})),v=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-item-template","title":"Course Item Template","category":"","description":"Course Item Template","textdomain":"learnpress","keywords":["course item","learnpress"],"usesContext":["lpCourseQuery"],"supports":{"align":true},"attributes":{"layout":{"type":"string","default":"list"}},"ancestor":["learnpress/list-courses"]}');(0,window.wp.blocks.registerBlockType)("learnpress/course-item-template",{...v,icon:{src:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"}))},edit:({clientId:r,context:s,attributes:i,setAttributes:c})=>{const p=(0,t.useBlockProps)({className:a()("learn-press-courses")}),[v,f]=(0,n.useState)(),[g,w]=(0,n.useState)(),[y,b]=(0,n.useState)([]),[h,k]=(0,n.useState)(0),{columns:C}=i,E=s.lpCourseQuery?.pagination_type||"number";(0,n.useEffect)((()=>{var e;const t=null!==(e=s.lpCourseQuery)&&void 0!==e?e:{};let r,a;return(async()=>{try{k(1),a=new AbortController,r=a.signal;const e=await(async(e,t)=>{const r=u.apiCourses;let s="?return_type=json";e&&(s+=`&${new URLSearchParams(e).toString()}`);const a=await fetch(r+s,{method:"GET",signal:t});if(!a.ok)throw new Error(`HTTP error! Status: ${a.status}`);return await a.json()})(t,r),{data:s}=e,{courses:o,page:n,total:l,total_pages:i}=s;w(e),b(o)}catch(e){"AbortError"!==e.name&&console.error("Failed to fetch courses:",e)}finally{k(0)}})(),()=>{a.abort()}}),[s.lpCourseQuery?.order_by,s.lpCourseQuery?.limit]);const{blocks:_}=(0,o.useSelect)((e=>{const{getBlocks:s}=e(t.store);return{blocks:s(r)}}),[r]),x=(0,n.useMemo)((()=>y?.map((e=>({lpCourseData:e,courseId:e?.ID})))),[y]);if(h)return(0,e.createElement)("ul",{...p},(0,e.createElement)("li",null,(0,l.__)("Courses Fetching…","learnpress")));if(0===y.length&&!h){const e=[{ID:1,post_title:(0,l.__)("Course One","learnpress")},{ID:2,post_title:(0,l.__)("Course two","learnpress")}];b(e)}return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("ul",{className:"learn-press-courses wp-block-learn-press-courses","data-layout":i.layout?i.layout:"list"},x&&x.map((r=>(0,e.createElement)(t.BlockContextProvider,{key:r.courseId,value:r},r.courseId===(v||x[0]?.courseId)?(0,e.createElement)(d,{classList:r.classList}):null,(0,e.createElement)(m,{blocks:_,blockContextId:r.courseId,classList:r.classList,setActiveBlockContextId:f,isHidden:r.courseId===(v||x[0]?.courseId)}))))),s.lpCourseQuery?.pagination?function(t){switch(t){case"load-more":return(0,e.createElement)("button",{className:"courses-btn-load-more"},(0,l.__)("Load More","learnpress"));case"infinite":return"";default:return(0,e.createElement)("nav",{className:"learnpress-block-pagination navigation pagination"},(0,e.createElement)("ul",{className:"page-numbers"},(0,e.createElement)("li",null,(0,e.createElement)("a",{className:"prev page-numbers",href:"?paged=1"},(0,e.createElement)("i",{className:"lp-icon-arrow-left"}))),Array.from({length:3},((t,r)=>(0,e.createElement)("li",{key:r},(0,e.createElement)("a",{className:"page-numbers",href:"{index}"},r+1))))))}}(E):null)},save:r=>{const s=t.useBlockProps.save();return(0,e.createElement)("div",{...s},(0,e.createElement)(t.InnerBlocks.Content,null))}})})()})();