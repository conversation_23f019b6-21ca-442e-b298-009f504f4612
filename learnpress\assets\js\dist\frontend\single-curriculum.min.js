(()=>{var e={1537:()=>{LP.Hook.addAction("lp-compatible-builder",(()=>{LP.Hook.removeAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend&&[...document.querySelectorAll("#popup-content")][0].addEventListener("scroll",(()=>{window.dispatchEvent(new Event("resize"))}))})),LP.Hook.addAction("lp-quiz-compatible-builder",(()=>(LP.Hook.removeAction("lp-quiz-compatible-builder"),LP.Hook.doAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend?window.elementorFrontend.init():"undefined"!=typeof vc_js?("undefined"!=typeof vc_round_charts&&vc_round_charts(),"undefined"!=typeof vc_pieChart&&vc_pieChart(),"undefined"!=typeof vc_line_charts&&vc_line_charts(),window.vc_js()):void 0))),LP.Hook.addAction("lp-question-compatible-builder",(()=>(LP.Hook.removeAction("lp-question-compatible-builder"),LP.Hook.removeAction("lp-quiz-compatible-builder"),LP.Hook.doAction("lp-compatible-builder"),"undefined"!=typeof elementorFrontend?window.elementorFrontend.init():"undefined"!=typeof vc_js?("undefined"!=typeof vc_round_charts&&vc_round_charts(),"undefined"!=typeof vc_pieChart&&vc_pieChart(),"undefined"!=typeof vc_line_charts&&vc_line_charts(),window.vc_js()):void 0)))}},t={};function o(s){var n=t[s];if(void 0!==n)return n.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,o),r.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.element,s=()=>{const e=document.querySelector("#popup-course"),t=document.querySelector("#learn-press-course-curriculum");if(e&&t){const o=t.querySelector(".curriculum-sections"),s=e.querySelector(".search-course"),n=e.querySelector('.search-course input[type="text"]');if(!n||!o||!s)return;const r=o.querySelectorAll("li.section"),a=o.querySelectorAll("li.course-item"),i=[];a.forEach((e=>{const t=e.dataset.id,o=e.querySelector(".item-name");i.push({id:t,name:o?o.textContent.toLowerCase():""})}));const l=e=>{e.preventDefault();const t=n.value;s.classList.add("searching"),t||s.classList.remove("searching");const o=[];i.forEach((e=>{t&&!e.name.match(t.toLowerCase())||(o.push(e.id),a.forEach((e=>{-1!==o.indexOf(e.dataset.id)?e.classList.remove("hide-if-js"):e.classList.add("hide-if-js")})))})),r.forEach((e=>{const t=e.querySelectorAll(".course-item"),s=[];t.forEach((e=>{o.includes(e.dataset.id)&&s.push(e.dataset.id)})),0===s.length?e.classList.add("hide-if-js"):e.classList.remove("hide-if-js")}))},c=s.querySelector(".clear");c&&c.addEventListener("click",(e=>{e.preventDefault(),n.value="",l(e)})),s.addEventListener("submit",l),n.addEventListener("keyup",l)}},n=jQuery,{throttle:r}=lodash,a=()=>{const e=document.querySelector("#sidebar-toggle");e&&(n(window).innerWidth()<=768||LP.Cookies.get("sidebar-toggle")?e.setAttribute("checked","checked"):e.removeAttribute("checked"),document.querySelector("#popup-course").addEventListener("click",(e=>{var t;"sidebar-toggle"===e.target.id&&(LP.Cookies.set("sidebar-toggle",!!e.target.checked),t=LP.Cookies.get("sidebar-toggle"),n("body").removeClass("lp-sidebar-toggle__open"),n("body").removeClass("lp-sidebar-toggle__close"),t?n("body").addClass("lp-sidebar-toggle__close"):n("body").addClass("lp-sidebar-toggle__open"))}))),n("#learn-press-course-curriculum").find(".section-desc").each(((e,t)=>{const o=n('<span class="show-desc"></span>').on("click",(()=>{s.toggleClass("c")})),s=n(t).siblings(".section-title").append(o)})),n(".section").each((function(){const e=n(this);e.find(".section-left").on("click",(function(){const t=e.toggleClass("closed").hasClass("closed"),o=LP.Cookies.get("closed-section-"+lpGlobalSettings.post_id)||[],s=parseInt(e.data("section-id")),n=o.findIndex((e=>e==s));t?o.push(parseInt(e.data("section-id"))):o.splice(n,1),LP.Cookies.remove("closed-section-(.*)"),LP.Cookies.set("closed-section-"+lpGlobalSettings.post_id,[...new Set(o)])}))}))},i=jQuery,l=()=>{i(".learn-press-progress").each((function(){const e=i(this).find(".learn-press-progress__active"),t=e.data("value");void 0!==t&&e.css("left",-(100-parseInt(t))+"%")}))},c=window.wp.url,d=jQuery;let u=null;const p={elLPOverlay:null,elMainContent:null,elTitle:null,elBtnYes:null,elBtnNo:null,elFooter:null,elCalledModal:null,callBackYes:null,instance:null,init(){return!!this.instance||(this.elLPOverlay=d(".lp-overlay"),!!this.elLPOverlay.length&&(u=this.elLPOverlay,this.elMainContent=u.find(".main-content"),this.elTitle=u.find(".modal-title"),this.elBtnYes=u.find(".btn-yes"),this.elBtnNo=u.find(".btn-no"),this.elFooter=u.find(".lp-modal-footer"),d(document).on("click",".close, .btn-no",(function(){u.hide()})),d(document).on("click",".btn-yes",(function(e){e.preventDefault(),e.stopPropagation(),"function"==typeof p.callBackYes&&p.callBackYes(e)})),this.instance=this,!0))},setElCalledModal(e){this.elCalledModal=e},setContentModal(e,t){this.elMainContent.html(e),"function"==typeof t&&t()},setTitleModal(e){this.elTitle.html(e)}},m=p,g={elBtnFinishCourse:null,elBtnCompleteItem:null,init(){m.init()&&void 0!==lpGlobalSettings&&"yes"===lpGlobalSettings.option_enable_popup_confirm_finish&&(this.elBtnFinishCourse=document.querySelectorAll(".lp-btn-finish-course"),this.elBtnCompleteItem=document.querySelector(".lp-btn-complete-item"),this.elBtnCompleteItem&&this.elBtnCompleteItem.addEventListener("click",(e=>{e.preventDefault();const t=e.target.closest("form");m.elLPOverlay.show(),m.setTitleModal(t.dataset.title);const o=document.createElement("div");o.appendChild(document.createTextNode(t.dataset.confirm));const s=o.innerHTML;m.setContentModal('<div class="pd-2em">'+s+"</div>"),m.callBackYes=()=>{t.submit()}})),this.elBtnFinishCourse&&this.elBtnFinishCourse.forEach((e=>e.addEventListener("click",(e=>{e.preventDefault();const t=e.target.closest("form");m.elLPOverlay.show(),m.setTitleModal(t.dataset.title),m.setContentModal('<div class="pd-2em">'+t.dataset.confirm+"</div>"),m.callBackYes=()=>{t.submit()}})))))}};o(1537);class f extends t.Component{checkCourseDurationExpire(){const e=document.getElementsByName("lp-course-timestamp-remaining");if(e.length){const t=e[0].value;t<86400&&setTimeout((function(){window.location.reload(!0)}),1e3*t)}}render(){return(0,e.createElement)("div",null)}}document.addEventListener("DOMContentLoaded",(()=>{LP.Hook.doAction("lp-compatible-builder"),s(),a(),l(),(()=>{const e=document.querySelectorAll(".popup-header__inner");if(e.length&&null===document.querySelector("#learn-press-quiz-app div.quiz-result")&&0===e[0].querySelectorAll("form.form-button-finish-course").length&&0!==(lpGlobalSettings.user_id||0)&&"IntersectionObserver"in window){const t=new IntersectionObserver(((e,o)=>{e.forEach((e=>{if(e.isIntersecting){const o=e.target;(async e=>{const t=await wp.apiFetch({path:(0,c.addQueryArgs)("lp/v1/lazy-load/items-progress",{courseId:lpGlobalSettings.post_id||"",userId:lpGlobalSettings.user_id||""}),method:"GET"}),{data:o}=t;e.innerHTML+=o,e.classList.add("can-finish-course"),g.init()})(o),t.unobserve(o)}}))}));[...e].map((e=>t.observe(e)))}})(),(new f).checkCourseDurationExpire()}));const y=jQuery,h={init(){this.scrollToItemViewing=function(){const e=y(".viewing-course-item");if(e.length){const t=y("#learn-press-course-curriculum"),o=(y("#popup-sidebar").outerHeight(),y(".section-title").outerHeight(),y(".section-header").outerHeight()),s=new RegExp("^viewing-course-item-([0-9].*)"),n=e.attr("class").split(/\s+/);let r=0;if(y.each(n,(function(e,t){const o=s.exec(t);if(o)return r=o[1],!1})),0===r)return;const a=y(".course-item-"+r);a.addClass("current"),a.offset().top;const i=a.offset().top-t.offset().top+t.scrollTop();t.animate({scrollTop:LP.Hook.applyFilters("scroll-item-current",i-o)},800)}},this.scrollToItemViewing()}},v=window.wp.apiFetch;var b=o.n(v);document.addEventListener("DOMContentLoaded",(function(e){LP.Hook.doAction("course-ready"),g.init(),function(){const e=async(e,t=1)=>{const o=parseInt(e.dataset.courseId),s=parseInt(e.dataset.itemId),n=e.closest(".lp-list-material"),r=e.querySelector(".course-material-table"),a=document.querySelector(".lp-loadmore-material"),i=document.querySelector(".lp-list-material"),l=e.querySelector(".lp-skeleton-animation");try{const e=await b()({path:"lp/v1/material/by-item",data:{course_id:o,item_id:s,page:t},method:"POST"}),{data:c,status:d,message:u}=e;if(l&&l.remove(),"success"!==d)return void n.insertAdjacentHTML("beforeend",u);c.items&&c.items.length>0?(r.style.display="table",r.querySelector("tbody").insertAdjacentHTML("beforeend",c.items)):i.innerHTML=u,c.load_more?(a.style.display="inline-block",a.setAttribute("page",t+1),a.classList.contains("loading")&&a.classList.remove("loading")):a.style.display="none"}catch(e){console.log(e.message)}};(()=>{const t=document.querySelector(".lp-material-skeleton");if(!t)return;const o=t.querySelector(".lp-loadmore-material");t.querySelector(".course-material-table").style.display="none",o.style.display="none",e(t)})(),document.addEventListener("click",(function(t){const o=t.target;if(o.classList.contains("lp-loadmore-material")){const t=document.querySelector(".lp-material-skeleton"),s=parseInt(o.getAttribute("page"));o.classList.add("loading"),e(t,s)}}))}()}));const _=setInterval((function(){document.querySelector(".learnpress-course-curriculum")&&(function(e=""){let t=0;const o=document.querySelector(".viewing-course-item");if(o){const e=new RegExp("^viewing-course-item-([0-9].*)");o.classList.forEach((function(o){const s=e.exec(o);if(s)return t=s[1],!1}))}let n=!1,r=!1;const a=async({ele:e,returnData:t,sectionID:o,itemID:s,data2:n,pages2:r,page2:a})=>{const l=(new DOMParser).parseFromString(t,"text/html");if(n){const e=l.querySelector(".curriculum-sections"),t=l.querySelector(".curriculum-more__button");t&&(r<=a?t.remove():t.dataset.page=a),e.insertAdjacentHTML("beforeend",n)}const c=l.querySelector(`[data-section-id="${o}"]`);if(c){const e=[...c.querySelectorAll(".course-item")].map((e=>e.dataset.id)),t=c.querySelector(".section-content"),n=c.querySelector(".section-item__loadmore");if(s&&!e.includes(s)){const e=await i("",2,o,s),{data3:r,pages3:a,paged3:l,page:c}=e;a<=l||a<=c?n.remove():n.dataset.page=c,r&&t&&t.insertAdjacentHTML("beforeend",r)}}e.insertAdjacentHTML("beforeend",l.body.innerHTML),h.init()},i=async(e,t,o,s)=>{let r=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum-items/";r=(0,c.addQueryArgs)(r,{sectionId:o||"",page:t});let a={};0!==parseInt(lpData.user_id)&&(a={headers:{"X-WP-Nonce":lpData.nonce}});let l=await fetch(r,{method:"GET",...a});l=await l.json();const{data:d,status:u,pages:p,message:m}=l,{page:g}=d;let f;if("success"===u){const n=d.content;if(f=d.item_ids,e+=n,o&&f&&s&&!f.includes(s))return i(e,t+1,o,s)}return n=!1,{data3:e,pages3:p||d.pages,status3:u,message3:m,page:g||0}},l=async(t,o,s)=>{let n=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum/";n=(0,c.addQueryArgs)(n,{courseId:e||lpGlobalSettings.post_id||"",page:o,sectionID:s||"",loadMore:!0});let a={};0!==parseInt(lpData.user_id)&&(a={headers:{"X-WP-Nonce":lpData.nonce}});let i=await fetch(n,{method:"GET",...a});i=await i.json();const{data:d,status:u,message:p}=i,m=d.content,g=d.section_ids,f=d.pages;return"success"===u&&(t+=m,s&&g&&g.length>0&&!g.includes(s))?l(t,o+1,s):(r=!1,{data2:t,pages2:f||d.pages,page2:o,status2:u,message2:p})};(()=>{const o=document.querySelector(".learnpress-course-curriculum");o&&(async o=>{const n=o.querySelector(".lp-skeleton-animation"),r=o.dataset.id,i=o.dataset.section;try{const s=1;let n=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum/";n=(0,c.addQueryArgs)(n,{courseId:e||lpGlobalSettings.post_id||"",page:s,sectionID:i||"",idItemViewing:t});let d={};0!==parseInt(lpData.user_id)&&(d={headers:{"X-WP-Nonce":lpData.nonce}});let u=await fetch(n,{method:"GET",...d});u=await u.json();const{data:p,status:m,message:g}=u,f=p.section_ids;if("error"===m)throw new Error(g||"Error");const y=p.content;if(i)if(f&&!f.includes(i)){const e=await l("",s+1,i);if(e){const{data2:t,pages2:s,page2:n}=e;await a({ele:o,returnData:y,sectionID:i,itemID:r,data2:t,pages2:s,page2:n})}}else await a({ele:o,returnData:y,sectionID:i,itemID:r});else y&&o.insertAdjacentHTML("beforeend",y)}catch(e){o.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}n&&n.remove(),s()})(o)})(),document.addEventListener("click",(e=>{[...document.querySelectorAll(".section-item__loadmore")].map((async t=>{if(t.contains(e.target)&&!n){n=!0;const e=t.parentNode,o=e.getAttribute("data-section-id"),r=e.querySelector(".section-content"),a=parseInt(t.dataset.page);t.classList.add("loading");try{const e=await i("",a+1,o,""),{data3:s,pages3:n,status3:l,message3:c}=e;if("error"===l)throw new Error(c||"Error");n<=a+1?t.remove():t.dataset.page=a+1,r.insertAdjacentHTML("beforeend",s)}catch(e){r.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}t.classList.remove("loading"),s()}})),[...document.querySelectorAll(".curriculum-more__button")].map((async t=>{if(t.contains(e.target)&&!r){r=!0;const e=parseInt(t.dataset.page),o=t.parentNode.parentNode.querySelector(".curriculum-sections");if(e&&o){t.classList.add("loading");try{const s=await l("",e+1,""),{data2:n,pages2:r,status2:a,message2:i}=s;if("error"===a)throw new Error(i||"Error");r<=e+1?t.remove():t.dataset.page=e+1,o.insertAdjacentHTML("beforeend",n)}catch(e){o.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}t.classList.remove("loading"),s()}}})),document.querySelector(".learnpress-course-curriculum")&&[...document.querySelectorAll(".section")].map((t=>{t.contains(e.target)&&t.querySelector(".section-left").contains(e.target)&&t.classList.toggle("closed")}))}))}(),clearInterval(_))}),1)})()})();