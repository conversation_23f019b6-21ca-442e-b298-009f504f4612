(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"lp-section-instructor"},(0,e.createElement)("h3",{className:"section-title"},"Instructor"),(0,e.createElement)("div",{className:"lp-instructor-info"},(0,e.createElement)("div",{className:"instructor-avatar"},(0,e.createElement)("img",{src:"https://placehold.co/160x160?text=Instructor"})),(0,e.createElement)("div",{className:"lp-section-instructor"},(0,e.createElement)("span",{className:"instructor-display-name"},(0,t.__)("Instructor Name","learnpress")),(0,e.createElement)("div",{className:"lp-instructor-meta"},(0,e.createElement)("div",{className:"instructor-item-meta"},(0,e.createElement)("span",{className:"instructor-total-students"},"2 Students")),(0,e.createElement)("div",{className:"instructor-item-meta"},(0,e.createElement)("span",{className:"instructor-total-courses"},"12 Courses"))),(0,e.createElement)("div",{className:"instructor-description"},(0,e.createElement)("p",null,"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua")))))))},n=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-instructor-info","title":"Course Instructor Info","category":"learnpress-course-elements","icon":"index-card","description":"Renders template Instructor Section PHP templates.","textdomain":"learnpress","keywords":["instructor info single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,o=window.wp.data;let i=null;var c,u,m;c=["learnpress/learnpress//single-lp_course"],u=a,m=e=>{(0,l.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,o.subscribe)((()=>{const e={...u},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&i!==r&&(i=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,l.registerBlockType)(a.name,{...a,edit:s,save:n})})();