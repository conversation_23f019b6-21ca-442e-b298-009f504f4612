(()=>{"use strict";const e=window.React,r=window.wp.i18n,s=window.wp.blockEditor,o=window.wp.components,t=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"learnpress/archive-course","title":"Archive Course","category":"learnpress-category","description":"Renders template Archive Course PHP templates.","textdomain":"learnpress","keywords":["archive course","learnpress"],"usesContext":[],"supports":{}}');(0,window.wp.blocks.registerBlockType)("learnpress/archive-course",{...t,edit:t=>{const n=(0,s.useBlockProps)();return(0,e.createElement)("div",{...n},(0,e.createElement)(o.Placeholder,{label:(0,r.__)("Archive Course","learnpress")},(0,e.createElement)("div",null,(0,r.__)("This is an editor placeholder for the Archive Course page. Content will render content of list courses. Should be not remove it","realpress"))))},save:e=>null})})();