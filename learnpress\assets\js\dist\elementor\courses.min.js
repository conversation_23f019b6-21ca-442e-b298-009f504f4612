(()=>{"use strict";const e=(e,t,n)=>{const s=new Date;s.setTime(s.getTime()+24*n*60*60*1e3);const o="expires="+s.toUTCString();document.cookie=e+"="+t+";"+o+";path=/"};window.lpElWidgetCoursesByPage=(()=>{const t="list-courses-elm-wrapper",n="list-courses-elm",s="learn-press-pagination",o="courses-page-result",r="lp-skeleton-animation";let c={};const i=(()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e})();let a,d="number",l=!0,u=!1;const f=(e,t={})=>{var c,i;console.log("/*** loadApiCoursesOfWidget ***/");const f=e.dataset.widgetId;let p,y=window[`lpWidget_${f}`];if(y&&"yes"===y.courses_rest)if("yes"===y.courses_rest_no_load_page&&l)l=!1;else{switch(y={...y,...t},a=null!==(c=y.lp_rest_url)&&void 0!==c?c:"",d=null!==(i=y.courses_rest_pagination_type)&&void 0!==i?i:"number",d){case"load-more":p=(e=>{const t=e.querySelector(".courses-btn-load-more");let o;t&&(o=t.querySelector(".lp-loading-circle"));const c=e.querySelector(`.${r}`),i=e.querySelector(`.${n}`);return{before:()=>{t&&(o.classList.remove("hide"),t.setAttribute("disabled","disabled"))},success:o=>{const r=document.createElement("div");r.innerHTML=o.data.content||"";const c=r.querySelector(`.${n}`),a=r.querySelector(`.${s}`);i?(i.insertAdjacentHTML("beforeend",c.innerHTML),a||t.remove()):e.insertAdjacentHTML("beforeend",o.data.content||""),o.data.pagination&&e.insertAdjacentHTML("beforeend",o.data.pagination||"")},error:e=>{},completed:()=>{c&&(c.style.display="none"),t&&(o.classList.add("hide"),t.removeAttribute("disabled"))}}})(e);break;case"infinite":p=(e=>{const t=e.querySelector(`.${r}`),o=e.querySelector(".courses-load-infinite");let c;o&&(c=o.querySelector(".lp-loading-circle"));const i=e.querySelector(`.${n}`);return{before:()=>{u=!0,c&&c.classList.remove("hide")},success:t=>{const r=document.createElement("div");r.innerHTML=t.data.content||"";const c=r.querySelector(`.${n}`),a=r.querySelector(`.${s}`);i?(i.insertAdjacentHTML("beforeend",c.innerHTML),a||o.remove()):e.insertAdjacentHTML("beforeend",t.data.content||""),t.data.pagination&&e.insertAdjacentHTML("beforeend",t.data.pagination||"")},error:e=>{},completed:()=>{u=!1,t&&(t.style.display="none"),o&&c.classList.add("hide")}}})(e);break;default:p=(e=>{if(!e)return;const t=e.querySelector(`.${r}`),c=e.querySelector(`.${n}`),i=e.querySelector(`.${s}`),a=e.querySelector(`.${o}`);return{before:()=>{},success:t=>{const r=document.createElement("div");r.innerHTML=t.data.content||"";const d=r.querySelector(`.${n}`),l=r.querySelector(`.${s}`),u=r.querySelector(`.${o}`);if(c){c.innerHTML=d.innerHTML,i&&(i.innerHTML=l.innerHTML),a&&(a.innerHTML=u.innerHTML);const e={behavior:"smooth"};c.scrollIntoView(e)}else e.insertAdjacentHTML("beforeend",t.data.content||"");t.data.pagination&&e.insertAdjacentHTML("beforeend",t.data.pagination||"")},error:e=>{},completed:()=>{t&&(t.style.display="none")}}})(e)}p&&((e,t={})=>{const n={method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}};void 0!==e.nonce&&(n.headers["X-WP-Nonce"]=e.nonce),((e,t={},n={})=>{"function"==typeof n.before&&n.before(),fetch(e,{method:"GET",...t}).then((e=>e.json())).then((e=>{"function"==typeof n.success&&n.success(e)})).catch((e=>{"function"==typeof n.error&&n.error(e)})).finally((()=>{"function"==typeof n.completed&&n.completed()}))})(a+"lp/v1/courses/courses-widget-by-page",n,t)})(y,p)}},p=()=>{document.addEventListener("change",(function(e){((e,n)=>{if(!n.classList.contains("courses-order-by"))return;const s=n.closest(`.${t}`);if(!s)return;e.preventDefault();const o=s.dataset.widgetId,r=window[`lpWidget_${o}`];c.order_by=n.value,c.paged=1,"yes"!==r.courses_rest?window.location.href=((e,t)=>{const n=new URL(e);return Object.keys(t).forEach((e=>{n.searchParams.set(e,t[e])})),n})(i,c):f(s,c)})(e,e.target)})),document.addEventListener("click",(function(s){const o=s.target;g(s,o),y(s,o),((s,o)=>{if(!o.classList.contains("courses-layout")){if(!o.closest(".courses-layout"))return;o=o.closest(".courses-layout")}const r=o.closest(`.${t}`);if(!r)return;s.preventDefault();const c=r.querySelector(`.${n}`),i=o.closest(".courses-layouts-display-list"),a=r.dataset.widgetId;i.querySelectorAll("li").forEach((e=>{e.classList.remove("active")}));const d=o.dataset.layout;o.classList.add("active"),c.classList.remove("grid","list"),c.classList.add(d),e(`layout_widget_${a}`,d,7)})(s,o)})),document.addEventListener("scroll",(function(e){const t=e.target;m(e,t)})),document.addEventListener("keyup",(function(e){e.target})),document.addEventListener("submit",(function(e){e.target}))},y=(e,n)=>{if(!n.classList.contains("page-numbers")){if(!n.closest(".page-numbers"))return;n=n.closest(".page-numbers")}const s=n.closest(`.${t}`);if(!s)return;const o=s.dataset.widgetId,r=window[`lpWidget_${o}`];if(!r||"yes"!==r.courses_rest)return;e.preventDefault();const i=c.paged;n.classList.contains("prev")?c.paged=i-1:n.classList.contains("next")?c.paged=i+1:c.paged=parseInt(n.textContent),f(s,c)},g=(e,n)=>{if(!n.classList.contains("courses-btn-load-more")){if(!n.closest(".courses-btn-load-more"))return;n=n.closest(".courses-btn-load-more")}const s=n.closest(`.${t}`);if(!s)return;const o=s.dataset.widgetId,r=window[`lpWidget_${o}`];r&&"yes"===r.courses_rest&&(e.preventDefault(),++c.paged,f(s,c))},m=(e,n)=>{const s=document.querySelector(`.${t}`);if(!s)return;const o=s.querySelector(".courses-load-infinite");if(!o)return;const r=s.dataset.widgetId,i=window[`lpWidget_${r}`];if(!i||"yes"!==i.courses_rest)return;const a=new IntersectionObserver((function(e){for(const t of e)if(t.isIntersecting){if(u)return;++c.paged,f(s,c),a.unobserve(t.target)}}));a.observe(o)};return{init:()=>{const e={},n=window.location.search,s=new URLSearchParams(n);for(const[t,n]of s.entries())e[t]=n;c={...e},c.paged=parseInt(c.paged||1),(()=>{const e=document.querySelectorAll(`.${t}`);e&&e.forEach((e=>{f(e)}))})(),p()}}})(),document.addEventListener("DOMContentLoaded",(function(){window.lpElWidgetCoursesByPage.init()}))})();