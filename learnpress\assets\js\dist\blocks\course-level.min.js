(()=>{"use strict";const e=window.React,t=window.wp.i18n,l=window.wp.blockEditor,n=window.wp.components,s=(window.wp.element,s=>{const r=(0,l.useBlockProps)(),{attributes:a,setAttributes:o,context:i}=s,{lpCourseData:c}=i,p=c?.level||'<div class="course-count-level"><span class="course-level">All levels</span></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.ToggleControl,{label:(0,t.__)("Show Label","learnpress"),checked:a.showLabel,onChange:e=>{o({showLabel:e})}}),(0,e.createElement)(n.<PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:a.showIcon,onChange:e=>{o({showIcon:e})}}))),(0,e.createElement)("div",{...r},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},a.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-signal"></i>'}}),a.showLabel?"Level:":""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:p}}))))}),r=e=>null,a=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-level","title":"Course Level","category":"learnpress-course-elements","description":"Show level course.","textdomain":"learnpress","keywords":["level","learnpress"],"icon":"chart-bar","ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),o=window.wp.blocks,i=window.wp.data;let c=null;var p,u,m;p=["learnpress/learnpress//single-lp_course"],u=a,m=e=>{(0,o.registerBlockType)(e.name,{...e,edit:s,save:r})},(0,i.subscribe)((()=>{const e={...u},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const l=t.getCurrentPostId();null!==l&&c!==l&&(c=l,(0,o.getBlockType)(e.name)&&((0,o.unregisterBlockType)(e.name),p.includes(l)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,o.registerBlockType)(a.name,{...a,edit:s,save:r})})();