(()=>{"use strict";const e=window.React,s=window.wp.blockEditor,t=window.wp.data,o=window.wp.element,n=[["learnpress/course-title"]];function l({classList:t}){const o=(0,s.useInnerBlocksProps)({template:n,__unstableDisableLayoutClassNames:!0});return(0,e.createElement)("li",{...o})}const r=(0,o.memo)((function({blocks:t,blockContextId:o,classList:n,isHidden:l,setActiveBlockContextId:r}){const c=(0,s.__experimentalUseBlockPreview)({blocks:t}),a=()=>{r(o)},i={display:l?"none":void 0};return(0,e.createElement)("li",{...c,tabIndex:0,role:"button",onClick:a,onKeyPress:a,style:i})})),c=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-item","title":"Course Item","category":"learnpress-category","description":"Course Item","textdomain":"learnpress","keywords":["course item","learnpress"],"usesContext":[],"supports":{"align":true},"ancestor":["learnpress/list-courses"]}');(0,window.wp.blocks.registerBlockType)("learnpress/course-item",{...c,icon:{src:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"}))},edit:({clientId:n})=>{console.log(n);const c=(0,s.useBlockProps)(),[a,i]=(0,o.useState)(),{posts:p,blocks:d}=(0,t.useSelect)((e=>{const{getBlocks:t}=e(s.store);return{posts:e("core").getEntityRecords("postType","lp_course"),blocks:t(n)}}),[n]);console.log(p);const u=(0,o.useMemo)((()=>p?.map((e=>({postType:e.type,postId:e.id})))),[p]);return(0,e.createElement)("ul",{...c},u&&u.map((t=>(0,e.createElement)(s.BlockContextProvider,{key:t.postId,value:t},t.postId===(a||u[0]?.postId)?(0,e.createElement)(l,{classList:t.classList}):null,(0,e.createElement)(r,{blocks:d,blockContextId:t.postId,classList:t.classList,setActiveBlockContextId:i,isHidden:t.postId===(a||u[0]?.postId)})))))},save:t=>{const o=s.useBlockProps.save();return(0,e.createElement)("div",{...o},(0,e.createElement)(s.InnerBlocks.Content,null))}})})();