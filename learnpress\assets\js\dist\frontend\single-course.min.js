(()=>{var e={9455:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});var s=o(1601),n=o.n(s),i=o(6314),a=o.n(i)()(n());a.push([e.id,"/*!\n * Toastify js 1.12.0\n * https://github.com/apvarun/toastify-js\n * @license MIT licensed\n *\n * Copyright (C) 2018 Varun A P\n */\n\n.toastify {\n    padding: 12px 20px;\n    color: #ffffff;\n    display: inline-block;\n    box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(77, 96, 232, 0.3);\n    background: -webkit-linear-gradient(315deg, #73a5ff, #5477f5);\n    background: linear-gradient(135deg, #73a5ff, #5477f5);\n    position: fixed;\n    opacity: 0;\n    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);\n    border-radius: 2px;\n    cursor: pointer;\n    text-decoration: none;\n    max-width: calc(50% - 20px);\n    z-index: **********;\n}\n\n.toastify.on {\n    opacity: 1;\n}\n\n.toast-close {\n    background: transparent;\n    border: 0;\n    color: white;\n    cursor: pointer;\n    font-family: inherit;\n    font-size: 1em;\n    opacity: 0.4;\n    padding: 0 5px;\n}\n\n.toastify-right {\n    right: 15px;\n}\n\n.toastify-left {\n    left: 15px;\n}\n\n.toastify-top {\n    top: -150px;\n}\n\n.toastify-bottom {\n    bottom: -150px;\n}\n\n.toastify-rounded {\n    border-radius: 25px;\n}\n\n.toastify-avatar {\n    width: 1.5em;\n    height: 1.5em;\n    margin: -7px 5px;\n    border-radius: 2px;\n}\n\n.toastify-center {\n    margin-left: auto;\n    margin-right: auto;\n    left: 0;\n    right: 0;\n    max-width: fit-content;\n    max-width: -moz-fit-content;\n}\n\n@media only screen and (max-width: 360px) {\n    .toastify-right, .toastify-left {\n        margin-left: auto;\n        margin-right: auto;\n        left: 0;\n        right: 0;\n        max-width: fit-content;\n    }\n}\n",""]);const r=a},6314:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",s=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),s&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),s&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,s,n,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(s)for(var r=0;r<this.length;r++){var l=this[r][0];null!=l&&(a[l]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);s&&a[d[0]]||(void 0!==i&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=i),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),n&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=n):d[4]="".concat(n)),t.push(d))}},t}},1601:e=>{"use strict";e.exports=function(e){return e[1]}},5072:e=>{"use strict";var t=[];function o(e){for(var o=-1,s=0;s<t.length;s++)if(t[s].identifier===e){o=s;break}return o}function s(e,s){for(var i={},a=[],r=0;r<e.length;r++){var l=e[r],c=s.base?l[0]+s.base:l[0],d=i[c]||0,u="".concat(c," ").concat(d);i[c]=d+1;var p=o(u),f={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==p)t[p].references++,t[p].updater(f);else{var m=n(f,s);s.byIndex=r,t.splice(r,0,{identifier:u,updater:m,references:1})}a.push(u)}return a}function n(e,t){var o=t.domAPI(t);return o.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,n){var i=s(e=e||[],n=n||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var r=o(i[a]);t[r].references--}for(var l=s(e,n),c=0;c<i.length;c++){var d=o(i[c]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}i=l}}},7659:e=>{"use strict";var t={};e.exports=function(e,o){var s=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(o)}},540:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},5056:(e,t,o)=>{"use strict";e.exports=function(e){var t=o.nc;t&&e.setAttribute("nonce",t)}},7825:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(o){!function(e,t,o){var s="";o.supports&&(s+="@supports (".concat(o.supports,") {")),o.media&&(s+="@media ".concat(o.media," {"));var n=void 0!==o.layer;n&&(s+="@layer".concat(o.layer.length>0?" ".concat(o.layer):""," {")),s+=o.css,n&&(s+="}"),o.media&&(s+="}"),o.supports&&(s+="}");var i=o.sourceMap;i&&"undefined"!=typeof btoa&&(s+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(s,e,t.options)}(t,e,o)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},1113:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},7736:function(e){var t;t=function(e){var t=function(e){return new t.lib.init(e)};function o(e,t){return t.offset[e]?isNaN(t.offset[e])?t.offset[e]:t.offset[e]+"px":"0px"}function s(e,t){return!(!e||"string"!=typeof t||!(e.className&&e.className.trim().split(/\s+/gi).indexOf(t)>-1))}return t.defaults={oldestFirst:!0,text:"Toastify is awesome!",node:void 0,duration:3e3,selector:void 0,callback:function(){},destination:void 0,newWindow:!1,close:!1,gravity:"toastify-top",positionLeft:!1,position:"",backgroundColor:"",avatar:"",className:"",stopOnFocus:!0,onClick:function(){},offset:{x:0,y:0},escapeMarkup:!0,ariaLive:"polite",style:{background:""}},t.lib=t.prototype={toastify:"1.12.0",constructor:t,init:function(e){return e||(e={}),this.options={},this.toastElement=null,this.options.text=e.text||t.defaults.text,this.options.node=e.node||t.defaults.node,this.options.duration=0===e.duration?0:e.duration||t.defaults.duration,this.options.selector=e.selector||t.defaults.selector,this.options.callback=e.callback||t.defaults.callback,this.options.destination=e.destination||t.defaults.destination,this.options.newWindow=e.newWindow||t.defaults.newWindow,this.options.close=e.close||t.defaults.close,this.options.gravity="bottom"===e.gravity?"toastify-bottom":t.defaults.gravity,this.options.positionLeft=e.positionLeft||t.defaults.positionLeft,this.options.position=e.position||t.defaults.position,this.options.backgroundColor=e.backgroundColor||t.defaults.backgroundColor,this.options.avatar=e.avatar||t.defaults.avatar,this.options.className=e.className||t.defaults.className,this.options.stopOnFocus=void 0===e.stopOnFocus?t.defaults.stopOnFocus:e.stopOnFocus,this.options.onClick=e.onClick||t.defaults.onClick,this.options.offset=e.offset||t.defaults.offset,this.options.escapeMarkup=void 0!==e.escapeMarkup?e.escapeMarkup:t.defaults.escapeMarkup,this.options.ariaLive=e.ariaLive||t.defaults.ariaLive,this.options.style=e.style||t.defaults.style,e.backgroundColor&&(this.options.style.background=e.backgroundColor),this},buildToast:function(){if(!this.options)throw"Toastify is not initialized";var e=document.createElement("div");for(var t in e.className="toastify on "+this.options.className,this.options.position?e.className+=" toastify-"+this.options.position:!0===this.options.positionLeft?(e.className+=" toastify-left",console.warn("Property `positionLeft` will be depreciated in further versions. Please use `position` instead.")):e.className+=" toastify-right",e.className+=" "+this.options.gravity,this.options.backgroundColor&&console.warn('DEPRECATION NOTICE: "backgroundColor" is being deprecated. Please use the "style.background" property.'),this.options.style)e.style[t]=this.options.style[t];if(this.options.ariaLive&&e.setAttribute("aria-live",this.options.ariaLive),this.options.node&&this.options.node.nodeType===Node.ELEMENT_NODE)e.appendChild(this.options.node);else if(this.options.escapeMarkup?e.innerText=this.options.text:e.innerHTML=this.options.text,""!==this.options.avatar){var s=document.createElement("img");s.src=this.options.avatar,s.className="toastify-avatar","left"==this.options.position||!0===this.options.positionLeft?e.appendChild(s):e.insertAdjacentElement("afterbegin",s)}if(!0===this.options.close){var n=document.createElement("button");n.type="button",n.setAttribute("aria-label","Close"),n.className="toast-close",n.innerHTML="&#10006;",n.addEventListener("click",function(e){e.stopPropagation(),this.removeElement(this.toastElement),window.clearTimeout(this.toastElement.timeOutValue)}.bind(this));var i=window.innerWidth>0?window.innerWidth:screen.width;("left"==this.options.position||!0===this.options.positionLeft)&&i>360?e.insertAdjacentElement("afterbegin",n):e.appendChild(n)}if(this.options.stopOnFocus&&this.options.duration>0){var a=this;e.addEventListener("mouseover",(function(t){window.clearTimeout(e.timeOutValue)})),e.addEventListener("mouseleave",(function(){e.timeOutValue=window.setTimeout((function(){a.removeElement(e)}),a.options.duration)}))}if(void 0!==this.options.destination&&e.addEventListener("click",function(e){e.stopPropagation(),!0===this.options.newWindow?window.open(this.options.destination,"_blank"):window.location=this.options.destination}.bind(this)),"function"==typeof this.options.onClick&&void 0===this.options.destination&&e.addEventListener("click",function(e){e.stopPropagation(),this.options.onClick()}.bind(this)),"object"==typeof this.options.offset){var r=o("x",this.options),l=o("y",this.options),c="left"==this.options.position?r:"-"+r,d="toastify-top"==this.options.gravity?l:"-"+l;e.style.transform="translate("+c+","+d+")"}return e},showToast:function(){var e;if(this.toastElement=this.buildToast(),!(e="string"==typeof this.options.selector?document.getElementById(this.options.selector):this.options.selector instanceof HTMLElement||"undefined"!=typeof ShadowRoot&&this.options.selector instanceof ShadowRoot?this.options.selector:document.body))throw"Root element is not defined";var o=t.defaults.oldestFirst?e.firstChild:e.lastChild;return e.insertBefore(this.toastElement,o),t.reposition(),this.options.duration>0&&(this.toastElement.timeOutValue=window.setTimeout(function(){this.removeElement(this.toastElement)}.bind(this),this.options.duration)),this},hideToast:function(){this.toastElement.timeOutValue&&clearTimeout(this.toastElement.timeOutValue),this.removeElement(this.toastElement)},removeElement:function(e){e.className=e.className.replace(" on",""),window.setTimeout(function(){this.options.node&&this.options.node.parentNode&&this.options.node.parentNode.removeChild(this.options.node),e.parentNode&&e.parentNode.removeChild(e),this.options.callback.call(e),t.reposition()}.bind(this),400)}},t.reposition=function(){for(var e,t={top:15,bottom:15},o={top:15,bottom:15},n={top:15,bottom:15},i=document.getElementsByClassName("toastify"),a=0;a<i.length;a++){e=!0===s(i[a],"toastify-top")?"toastify-top":"toastify-bottom";var r=i[a].offsetHeight;e=e.substr(9,e.length-1),(window.innerWidth>0?window.innerWidth:screen.width)<=360?(i[a].style[e]=n[e]+"px",n[e]+=r+15):!0===s(i[a],"toastify-left")?(i[a].style[e]=t[e]+"px",t[e]+=r+15):(i[a].style[e]=o[e]+"px",o[e]+=r+15)}return this},t.lib.init.prototype=t.lib,t},e.exports?e.exports=t():this.Toastify=t()}},t={};function o(s){var n=t[s];if(void 0!==n)return n.exports;var i=t[s]={id:s,exports:{}};return e[s].call(i.exports,i,i.exports,o),i.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.nc=void 0,(()=>{"use strict";window.React,window.wp.element,window.LP.quiz;const e=jQuery,{throttle:t}=lodash,s=()=>{const t=document.querySelector("#sidebar-toggle");t&&(e(window).innerWidth()<=768||LP.Cookies.get("sidebar-toggle")?t.setAttribute("checked","checked"):t.removeAttribute("checked"),document.querySelector("#popup-course").addEventListener("click",(t=>{var o;"sidebar-toggle"===t.target.id&&(LP.Cookies.set("sidebar-toggle",!!t.target.checked),o=LP.Cookies.get("sidebar-toggle"),e("body").removeClass("lp-sidebar-toggle__open"),e("body").removeClass("lp-sidebar-toggle__close"),o?e("body").addClass("lp-sidebar-toggle__close"):e("body").addClass("lp-sidebar-toggle__open"))}))),e("#learn-press-course-curriculum").find(".section-desc").each(((t,o)=>{const s=e('<span class="show-desc"></span>').on("click",(()=>{n.toggleClass("c")})),n=e(o).siblings(".section-title").append(s)})),e(".section").each((function(){const t=e(this);t.find(".section-left").on("click",(function(){const e=t.toggleClass("closed").hasClass("closed"),o=LP.Cookies.get("closed-section-"+lpGlobalSettings.post_id)||[],s=parseInt(t.data("section-id")),n=o.findIndex((e=>e==s));e?o.push(parseInt(t.data("section-id"))):o.splice(n,1),LP.Cookies.remove("closed-section-(.*)"),LP.Cookies.set("closed-section-"+lpGlobalSettings.post_id,[...new Set(o)])}))}))};document.addEventListener("DOMContentLoaded",(()=>{s()}));const n=jQuery;let i=null;const a={elLPOverlay:null,elMainContent:null,elTitle:null,elBtnYes:null,elBtnNo:null,elFooter:null,elCalledModal:null,callBackYes:null,instance:null,init(){return!!this.instance||(this.elLPOverlay=n(".lp-overlay"),!!this.elLPOverlay.length&&(i=this.elLPOverlay,this.elMainContent=i.find(".main-content"),this.elTitle=i.find(".modal-title"),this.elBtnYes=i.find(".btn-yes"),this.elBtnNo=i.find(".btn-no"),this.elFooter=i.find(".lp-modal-footer"),n(document).on("click",".close, .btn-no",(function(){i.hide()})),n(document).on("click",".btn-yes",(function(e){e.preventDefault(),e.stopPropagation(),"function"==typeof a.callBackYes&&a.callBackYes(e)})),this.instance=this,!0))},setElCalledModal(e){this.elCalledModal=e},setContentModal(e,t){this.elMainContent.html(e),"function"==typeof t&&t()},setTitleModal(e){this.elTitle.html(e)}},r=a,l={elBtnFinishCourse:null,elBtnCompleteItem:null,init(){r.init()&&void 0!==lpGlobalSettings&&"yes"===lpGlobalSettings.option_enable_popup_confirm_finish&&(this.elBtnFinishCourse=document.querySelectorAll(".lp-btn-finish-course"),this.elBtnCompleteItem=document.querySelector(".lp-btn-complete-item"),this.elBtnCompleteItem&&this.elBtnCompleteItem.addEventListener("click",(e=>{e.preventDefault();const t=e.target.closest("form");r.elLPOverlay.show(),r.setTitleModal(t.dataset.title);const o=document.createElement("div");o.appendChild(document.createTextNode(t.dataset.confirm));const s=o.innerHTML;r.setContentModal('<div class="pd-2em">'+s+"</div>"),r.callBackYes=()=>{t.submit()}})),this.elBtnFinishCourse&&this.elBtnFinishCourse.forEach((e=>e.addEventListener("click",(e=>{e.preventDefault();const t=e.target.closest("form");r.elLPOverlay.show(),r.setTitleModal(t.dataset.title),r.setContentModal('<div class="pd-2em">'+t.dataset.confirm+"</div>"),r.callBackYes=()=>{t.submit()}})))))}},c=window.wp.url,d=jQuery,u={init(){this.scrollToItemViewing=function(){const e=d(".viewing-course-item");if(e.length){const t=d("#learn-press-course-curriculum"),o=(d("#popup-sidebar").outerHeight(),d(".section-title").outerHeight(),d(".section-header").outerHeight()),s=new RegExp("^viewing-course-item-([0-9].*)"),n=e.attr("class").split(/\s+/);let i=0;if(d.each(n,(function(e,t){const o=s.exec(t);if(o)return i=o[1],!1})),0===i)return;const a=d(".course-item-"+i);a.addClass("current"),a.offset().top;const r=a.offset().top-t.offset().top+t.scrollTop();t.animate({scrollTop:LP.Hook.applyFilters("scroll-item-current",r-o)},800)}},this.scrollToItemViewing()}},p=()=>{const e=document.querySelector("#popup-course"),t=document.querySelector("#learn-press-course-curriculum");if(e&&t){const o=t.querySelector(".curriculum-sections"),s=e.querySelector(".search-course"),n=e.querySelector('.search-course input[type="text"]');if(!n||!o||!s)return;const i=o.querySelectorAll("li.section"),a=o.querySelectorAll("li.course-item"),r=[];a.forEach((e=>{const t=e.dataset.id,o=e.querySelector(".item-name");r.push({id:t,name:o?o.textContent.toLowerCase():""})}));const l=e=>{e.preventDefault();const t=n.value;s.classList.add("searching"),t||s.classList.remove("searching");const o=[];r.forEach((e=>{t&&!e.name.match(t.toLowerCase())||(o.push(e.id),a.forEach((e=>{-1!==o.indexOf(e.dataset.id)?e.classList.remove("hide-if-js"):e.classList.add("hide-if-js")})))})),i.forEach((e=>{const t=e.querySelectorAll(".course-item"),s=[];t.forEach((e=>{o.includes(e.dataset.id)&&s.push(e.dataset.id)})),0===s.length?e.classList.add("hide-if-js"):e.classList.remove("hide-if-js")}))},c=s.querySelector(".clear");c&&c.addEventListener("click",(e=>{e.preventDefault(),n.value="",l(e)})),s.addEventListener("submit",l),n.addEventListener("keyup",l)}};function f(e=""){let t=0;const o=document.querySelector(".viewing-course-item");if(o){const e=new RegExp("^viewing-course-item-([0-9].*)");o.classList.forEach((function(o){const s=e.exec(o);if(s)return t=s[1],!1}))}let s=!1,n=!1;const i=async({ele:e,returnData:t,sectionID:o,itemID:s,data2:n,pages2:i,page2:r})=>{const l=(new DOMParser).parseFromString(t,"text/html");if(n){const e=l.querySelector(".curriculum-sections"),t=l.querySelector(".curriculum-more__button");t&&(i<=r?t.remove():t.dataset.page=r),e.insertAdjacentHTML("beforeend",n)}const c=l.querySelector(`[data-section-id="${o}"]`);if(c){const e=[...c.querySelectorAll(".course-item")].map((e=>e.dataset.id)),t=c.querySelector(".section-content"),n=c.querySelector(".section-item__loadmore");if(s&&!e.includes(s)){const e=await a("",2,o,s),{data3:i,pages3:r,paged3:l,page:c}=e;r<=l||r<=c?n.remove():n.dataset.page=c,i&&t&&t.insertAdjacentHTML("beforeend",i)}}e.insertAdjacentHTML("beforeend",l.body.innerHTML),u.init()},a=async(e,t,o,n)=>{let i=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum-items/";i=(0,c.addQueryArgs)(i,{sectionId:o||"",page:t});let r={};0!==parseInt(lpData.user_id)&&(r={headers:{"X-WP-Nonce":lpData.nonce}});let l=await fetch(i,{method:"GET",...r});l=await l.json();const{data:d,status:u,pages:p,message:f}=l,{page:m}=d;let h;if("success"===u){const s=d.content;if(h=d.item_ids,e+=s,o&&h&&n&&!h.includes(n))return a(e,t+1,o,n)}return s=!1,{data3:e,pages3:p||d.pages,status3:u,message3:f,page:m||0}},r=async(t,o,s)=>{let i=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum/";i=(0,c.addQueryArgs)(i,{courseId:e||lpGlobalSettings.post_id||"",page:o,sectionID:s||"",loadMore:!0});let a={};0!==parseInt(lpData.user_id)&&(a={headers:{"X-WP-Nonce":lpData.nonce}});let l=await fetch(i,{method:"GET",...a});l=await l.json();const{data:d,status:u,message:p}=l,f=d.content,m=d.section_ids,h=d.pages;return"success"===u&&(t+=f,s&&m&&m.length>0&&!m.includes(s))?r(t,o+1,s):(n=!1,{data2:t,pages2:h||d.pages,page2:o,status2:u,message2:p})};(()=>{const o=document.querySelector(".learnpress-course-curriculum");o&&(async o=>{const s=o.querySelector(".lp-skeleton-animation"),n=o.dataset.id,a=o.dataset.section;try{const s=1;let l=lpData.lp_rest_url+"lp/v1/lazy-load/course-curriculum/";l=(0,c.addQueryArgs)(l,{courseId:e||lpGlobalSettings.post_id||"",page:s,sectionID:a||"",idItemViewing:t});let d={};0!==parseInt(lpData.user_id)&&(d={headers:{"X-WP-Nonce":lpData.nonce}});let u=await fetch(l,{method:"GET",...d});u=await u.json();const{data:p,status:f,message:m}=u,h=p.section_ids;if("error"===f)throw new Error(m||"Error");const y=p.content;if(a)if(h&&!h.includes(a)){const e=await r("",s+1,a);if(e){const{data2:t,pages2:s,page2:r}=e;await i({ele:o,returnData:y,sectionID:a,itemID:n,data2:t,pages2:s,page2:r})}}else await i({ele:o,returnData:y,sectionID:a,itemID:n});else y&&o.insertAdjacentHTML("beforeend",y)}catch(e){o.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}s&&s.remove(),p()})(o)})(),document.addEventListener("click",(e=>{[...document.querySelectorAll(".section-item__loadmore")].map((async t=>{if(t.contains(e.target)&&!s){s=!0;const e=t.parentNode,o=e.getAttribute("data-section-id"),n=e.querySelector(".section-content"),i=parseInt(t.dataset.page);t.classList.add("loading");try{const e=await a("",i+1,o,""),{data3:s,pages3:r,status3:l,message3:c}=e;if("error"===l)throw new Error(c||"Error");r<=i+1?t.remove():t.dataset.page=i+1,n.insertAdjacentHTML("beforeend",s)}catch(e){n.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}t.classList.remove("loading"),p()}})),[...document.querySelectorAll(".curriculum-more__button")].map((async t=>{if(t.contains(e.target)&&!n){n=!0;const e=parseInt(t.dataset.page),o=t.parentNode.parentNode.querySelector(".curriculum-sections");if(e&&o){t.classList.add("loading");try{const s=await r("",e+1,""),{data2:n,pages2:i,status2:a,message2:l}=s;if("error"===a)throw new Error(l||"Error");i<=e+1?t.remove():t.dataset.page=e+1,o.insertAdjacentHTML("beforeend",n)}catch(e){o.insertAdjacentHTML("beforeend",`<div class="lp-ajax-message error" style="display:block">${e.message||"Error: Query lp/v1/lazy-load/course-curriculum"}</div>`)}t.classList.remove("loading"),p()}}})),document.querySelector(".learnpress-course-curriculum")&&[...document.querySelectorAll(".section")].map((t=>{t.contains(e.target)&&t.querySelector(".section-left").contains(e.target)&&t.classList.toggle("closed")}))}))}const m=window.wp.apiFetch;var h=o.n(m);const y="loading",g=(e,t)=>{e&&(t?e.classList.add(y):e.classList.remove(y))};var v=o(7736),b=o.n(v),w=o(5072),L=o.n(w),E=o(7825),S=o.n(E),k=o(7659),x=o.n(k),C=o(5056),T=o.n(C),_=o(540),q=o.n(_),M=o(1113),A=o.n(M),D=o(9455),I={};function P(e,t,o){const s=document.querySelectorAll(e);s.length&&s.forEach((s=>{const n=s.querySelector(t),i=e.replace(".","")+"__open",a=s.querySelector(e+"__close"),r=()=>s.classList.contains(i),l=()=>{r()&&s.classList.remove(i)};n&&(n.onclick=function(e){e.preventDefault(),r()?l():r()||s.classList.add(i)}),document.addEventListener("click",(e=>{if(!r())return;const s=e.target;s.closest(o)||s.closest(t)||l()})),a&&a.addEventListener("click",(e=>{e.preventDefault(),l()})),document.addEventListener("keydown",(e=>{27===e.keyCode&&l()}),!1)}))}I.styleTagTransform=A(),I.setAttributes=T(),I.insert=x().bind(null,"head"),I.domAPI=S(),I.insertStyleElement=q(),L()(D.A,I),D.A&&D.A.locals&&D.A.locals;const O=jQuery;document.addEventListener("DOMContentLoaded",(function(){O("#popup-course"),O("#learn-press-course-curriculum"),(()=>{const e=document.querySelectorAll(".course-extra-box");[...e].map((t=>{const o=t.querySelector(".course-extra-box__title");t.classList.remove("active");const s=t.querySelector(".course-extra-box__content");s.style.height="0",o.addEventListener("click",(()=>{const o=t.classList.contains("active");[...e].forEach((e=>{e!==t&&(e.classList.remove("active"),e.querySelector(".course-extra-box__content").style.height="0")})),o?(t.classList.remove("active"),s.style.height="0"):(t.classList.add("active"),s.style.height=s.scrollHeight+"px")}))}))})(),O("#learn-press-course-tabs").on("change",'input[name="learn-press-course-tab-radio"]',(function(e){const t=O('input[name="learn-press-course-tab-radio"]:checked').val();LP.Cookies.set("course-tab",t),O('label[for="'+O(e.target).attr("id")+'"]').closest("li").addClass("active").siblings().removeClass("active")})),function(){const e=O(".course-summary-sidebar");if(!e.length)return;const t=O(window),o=e.children(),s=e.offset();let n=0;const i=e.height(),a=o.height();t.on("scroll.fixed-course-sidebar",(function(){n=t.scrollTop();const r=n-s.top+32;if(r<0)return e.removeClass("slide-top slide-down"),void o.css("top","");r>i-a?(e.removeClass("slide-down").addClass("slide-top"),o.css("top",i-a)):(e.removeClass("slide-top").addClass("slide-down"),o.css("top",32))})).trigger("scroll.fixed-course-sidebar")}(),(()=>{const e=document.querySelectorAll("form.enroll-course");e.length>0&&e.forEach((e=>{e.addEventListener("submit",(t=>{t.preventDefault();const o=e.querySelector("input[name=enroll-course]").value,s=e.querySelector("button.button-enroll-course");g(s,1),(async(t,o)=>{try{const s=await wp.apiFetch({path:"lp/v1/courses/enroll-course",method:"POST",data:{id:t}}),{status:n,data:{redirect:i},message:a}=s;if("success"!==n)throw g(o,0),new Error(a);o.remove(),a&&n&&(e.innerHTML+=`<div class="learn-press-message ${n}">${a}</div>`,i&&(window.location.href=i))}catch(e){b()({text:e.message,gravity:lpData.toast.gravity,position:lpData.toast.position,className:`${lpData.toast.classPrefix} error`,close:1==lpData.toast.close,stopOnFocus:1==lpData.toast.stopOnFocus,duration:lpData.toast.duration}).showToast()}})(o,s)}))})),null!==document.querySelector(".course-detail-info")&&window.addEventListener("pageshow",(e=>{(e.persisted||void 0!==window.performance&&"back_forward"==String(window.performance.getEntriesByType("navigation")[0].type))&&location.reload()}))})(),(()=>{const e=document.querySelectorAll("form.purchase-course");e.length>0&&e.forEach((e=>{const t=async(o,s,n=!1)=>{try{const i=await wp.apiFetch({path:"lp/v1/courses/purchase-course",method:"POST",data:{id:o,repurchaseType:n}}),{status:a,data:{redirect:l,type:c,html:d,titlePopup:u},message:p}=i;if("success"!==a)throw g(s,0),new Error(p);if("allow_repurchase"===c?g(s,0):s.remove(),"allow_repurchase"===c&&"success"===a){if(!e.querySelector(".lp_allow_repurchase_select")){if(!r.init())return;r.elLPOverlay.show(),r.setTitleModal(u||""),r.setContentModal(d),r.callBackYes=()=>{r.elLPOverlay.hide(),document.querySelectorAll(".lp_allow_repurchase_select").forEach((o=>{const s=o.querySelectorAll("[name=_lp_allow_repurchase_type]");for(let o=0,n=s.length;o<n;o++)if(s[o].checked){const n=s[o].value,i=e.querySelector("input[name=purchase-course]").value,a=e.querySelector("button.button-purchase-course");g(a,1),t(i,a,n);break}}))}}}else p&&a&&(e.innerHTML+=`<div class="learn-press-message ${a}">${p}</div>`,"success"===a&&l&&(window.location.href=l))}catch(e){b()({text:e.message,gravity:lpData.toast.gravity,position:lpData.toast.position,className:`${lpData.toast.classPrefix} error`,close:1==lpData.toast.close,stopOnFocus:1==lpData.toast.stopOnFocus,duration:lpData.toast.duration}).showToast()}};e.addEventListener("submit",(o=>{o.preventDefault();const s=e.querySelector("input[name=purchase-course]").value,n=e.querySelector("button.button-purchase-course");g(n,1),t(s,n)}))}))})(),(()=>{const e=document.querySelectorAll(".lp-form-retake-course");e.length&&e.forEach((e=>{const t=e.querySelector(".button-retake-course"),o=e.querySelector("[name=retake-course]").value,s=e.querySelector(".lp-ajax-message");e.addEventListener("submit",(e=>{e.preventDefault()})),t.addEventListener("click",(e=>{var n;e.preventDefault(),t.classList.add("loading"),t.disabled=!0,n=t,wp.apiFetch({path:"/lp/v1/courses/retake-course",method:"POST",data:{id:o}}).then((e=>{const{status:t,message:o,data:i}=e;s.innerHTML=o,null!=t&&"success"===t?(n.style.display="none",setTimeout((()=>{window.location.replace(i.url_redirect)}),1e3)):s.classList.add("error")})).catch((e=>{s.classList.add("error"),s.innerHTML="error: "+e.message})).then((()=>{n.classList.remove("loading"),n.disabled=!1,s.style.display="block"}))}))}))})(),(()=>{const e=document.querySelectorAll(".lp-course-progress-wrapper");if(!e.length)return;if("IntersectionObserver"in window){const o=new IntersectionObserver(((e,s)=>{e.forEach((e=>{if(e.isIntersecting){const s=e.target;setTimeout((function(){t(s)}),600),o.unobserve(s)}}))}));[...e].map((e=>o.observe(e)))}const t=async e=>{let t="lp/v1/lazy-load/course-progress";lpData.urlParams.hasOwnProperty("lang")&&(t+="?lang="+lpData.urlParams.lang);const o=await wp.apiFetch({path:t,method:"POST",data:{courseId:lpGlobalSettings.post_id||"",userId:lpData.user_id||""}}),{data:s}=o;e.innerHTML=s}})(),(()=>{const e=document.querySelectorAll("form.continue-course");e.length&&lpData.user_id>0&&(async()=>await wp.apiFetch({path:"lp/v1/courses/continue-course",method:"POST",data:{courseId:lpGlobalSettings.post_id||"",userId:lpGlobalSettings.user_id||""}}))().then((function(t){"success"===t.status&&e.forEach((e=>{e.style.display="inline-block",e.action=t.data}))}))})(),l.init(),function(){const e=async(e,t=1)=>{const o=parseInt(e.dataset.courseId),s=parseInt(e.dataset.itemId),n=e.closest(".lp-list-material"),i=e.querySelector(".course-material-table"),a=document.querySelector(".lp-loadmore-material"),r=document.querySelector(".lp-list-material"),l=e.querySelector(".lp-skeleton-animation");try{const e=await h()({path:"lp/v1/material/by-item",data:{course_id:o,item_id:s,page:t},method:"POST"}),{data:c,status:d,message:u}=e;if(l&&l.remove(),"success"!==d)return void n.insertAdjacentHTML("beforeend",u);c.items&&c.items.length>0?(i.style.display="table",i.querySelector("tbody").insertAdjacentHTML("beforeend",c.items)):r.innerHTML=u,c.load_more?(a.style.display="inline-block",a.setAttribute("page",t+1),a.classList.contains("loading")&&a.classList.remove("loading")):a.style.display="none"}catch(e){console.log(e.message)}};(()=>{const t=document.querySelector(".lp-material-skeleton");if(!t)return;const o=t.querySelector(".lp-loadmore-material");t.querySelector(".course-material-table").style.display="none",o.style.display="none",e(t)})(),document.addEventListener("click",(function(t){const o=t.target;if(o.classList.contains("lp-loadmore-material")){const t=document.querySelector(".lp-material-skeleton"),s=parseInt(o.getAttribute("page"));o.classList.add("loading"),e(t,s)}}))}(),function(){const e=document.querySelector(".TabsDragScroll");if(!e)return;const t=e.querySelector("ul");let o=!1;t.addEventListener("mousemove",(e=>{o&&(t.scrollLeft-=e.movementX,t.classList.add("dragging"))})),document.addEventListener("mouseup",(()=>{o=!1,t.classList.remove("dragging")})),t.addEventListener("mousedown",(()=>{o=!0})),t.querySelectorAll("li").forEach((e=>{e.addEventListener("click",(()=>(e=>{const o=e.offsetLeft,s=e.offsetWidth,n=t.clientWidth/2-s/2;t.scrollTo({left:o-n,behavior:"smooth"})})(e)))}))}(),function(){P(".social-share-toggle",".share-toggle-icon",".content-widget-social-share");var e=document.querySelector(".btn-clipboard");e&&e.addEventListener("click",(function(t){var o=document.querySelector(".clipboard-value");o.focus(),o.select();try{document.execCommand("copy");var s=e.getAttribute("data-copied");e.innerHTML=s+'<span class="tooltip">'+s+"</span>"}catch(e){}}))}()}));const N=setInterval((function(){document.querySelector(".learnpress-course-curriculum")&&(f(),clearInterval(N))}),1);LP.Hook.addAction("lp_course_curriculum_skeleton",(function(e){f(e)}))})()})();