(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,r=window.wp.components,a=(window.wp.element,a=>{const s=(0,n.useBlockProps)(),{attributes:o,setAttributes:l,context:i}=a,{lpCourseData:c}=i,u=c?.duration||'<div class="course-count-duration"><span class="course-duration">10 Weeks</span></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(r.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(r.<PERSON><PERSON>,{label:(0,t.__)("Show Label","learnpress"),checked:o.showLabel,onChange:e=>{l({showLabel:e})}}),(0,e.createElement)(r.<PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:o.showIcon,onChange:e=>{l({showIcon:e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},a.attributes.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-clock-o"></i>'}}),a.attributes.showLabel?"Duration:":""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:u}}))))}),s=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-duration","title":"Course Duration","category":"learnpress-course-elements","description":"Show Duration of Course.","textdomain":"learnpress","keywords":["duration","learnpress"],"usesContext":["lpCourseData"],"icon":"clock","ancestor":["learnpress/single-course","learnpress/course-item-template"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,i=window.wp.data;let c=null;var u,p,m;u=["learnpress/learnpress//single-lp_course"],p=o,m=e=>{(0,l.registerBlockType)(e.name,{...e,edit:a,save:s})},(0,i.subscribe)((()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),u.includes(n)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,l.registerBlockType)(o.name,{...o,edit:a,save:s})})();