(()=>{"use strict";const e=window.React,r=window.wp.i18n,s=window.wp.components,t=window.wp.blockEditor,n=n=>{const o=(0,t.useBlockProps)();return(0,e.createElement)("div",{...o},(0,e.createElement)(s.Placeholder,{label:(0,r.__)("Course item curriculum (Legacy) - Don't remove","learnpress")},(0,e.createElement)("div",null,(0,r.__)("Displays the course curriculum, including lessons, quizzes, and other learning items, organized by sections or topics!","learnpress"))))},o=e=>null,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-item-curriculum","title":"Course Item Curriculum (Legacy)","category":"learnpress-course-elements","icon":"welcome-learn-more","description":"Renders Course Item Curriculum Block PHP Template.","textdomain":"learnpress","keywords":["item curriculum course single","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":true}}'),c=window.wp.blocks,u=window.wp.data;let i=null;var a,p,m;a=["learnpress/learnpress//single-lp_course_item"],p=l,m=e=>{(0,c.registerBlockType)(e.name,{...e,edit:n,save:o})},(0,u.subscribe)((()=>{const e={...p},r=(0,u.select)("core/editor")||null;if(!r||"function"!=typeof r.getCurrentPostId||!r.getCurrentPostId())return;const s=r.getCurrentPostId();null!==s&&i!==s&&(i=s,(0,c.getBlockType)(e.name)&&((0,c.unregisterBlockType)(e.name),a.includes(s)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,c.registerBlockType)(l.name,{...l,edit:n,save:o})})();