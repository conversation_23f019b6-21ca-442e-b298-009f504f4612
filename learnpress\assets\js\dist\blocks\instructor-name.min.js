(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-name","title":"Instructor Name","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Name PHP templates.","textdomain":"learnpress","keywords":["instructor name single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}'),s=window.wp.blocks,a=window.wp.primitives,l=window.ReactJSXRuntime,i=(0,l.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(a.Path,{d:"m4 5.5h2v6.5h1.5v-6.5h2v-1.5h-5.5zm16 10.5h-16v-1.5h16zm-7 4h-9v-1.5h9z"})});(0,s.registerBlockType)(r.name,{...r,icon:i,edit:r=>{const s=(0,n.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("h2",null,(0,e.createElement)("span",{className:"instructor-display-name"},(0,t.__)("Instructor's name","learnpress")))))},save:e=>null})})();