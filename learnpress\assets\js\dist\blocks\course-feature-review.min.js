(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,a=a=>{const s=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"featured-review__title"},(0,t.__)("Featured Review","learnpress")),(0,e.createElement)("div",{className:"featured-review__stars"},(0,e.createElement)("i",{className:"lp-icon-star"}),(0,e.createElement)("i",{className:"lp-icon-star"}),(0,e.createElement)("i",{className:"lp-icon-star"}),(0,e.createElement)("i",{className:"lp-icon-star"}),(0,e.createElement)("i",{className:"lp-icon-star"})),(0,e.createElement)("div",{className:"featured-review__content"},(0,e.createElement)("p",null,"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua"))))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-feature-review","title":"Course Feature Review","icon":"star-filled","category":"learnpress-course-elements","description":"Renders template Feature Review Course PHP templates.","textdomain":"learnpress","keywords":["feature review single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"align":["wide","full"],"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,i=window.wp.data;let o=null;var c,u,p;c=["learnpress/learnpress//single-lp_course"],u=n,p=e=>{(0,l.registerBlockType)(e.name,{...e,edit:a,save:s})},(0,i.subscribe)((()=>{const e={...u},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))})),(0,l.registerBlockType)(n.name,{...n,edit:a,save:s})})();