(()=>{"use strict";const e=window.React,t=window.wp.blockEditor,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-description","title":"Instructor Description","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Instructor Description PHP templates.","textdomain":"learnpress","keywords":["instructor description single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}');(0,window.wp.blocks.registerBlockType)(r.name,{...r,edit:r=>{const n=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)("div",{className:"instructor-description"},(0,e.createElement)("p",null,"It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout."))))},save:e=>null})})();