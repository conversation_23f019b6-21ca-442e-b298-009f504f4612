document.addEventListener("DOMContentLoaded",(function(){const e=["single-lp_course","archive-lp_course","taxonomy-course_tag","taxonomy-course_category"];let t=null;const o=(()=>{let o;return(...n)=>{clearTimeout(o),o=setTimeout((()=>(()=>{const o=wp?.data?.select("core/editor")?.getEditedPostAttribute("slug")||wp?.data?.select("core/editor")?.getCurrentPostId()||(()=>{const e=new URLSearchParams(window.location.search),t=e.get("p")?.replace(/^\/|\/$/g,"");return"template"===t?t:null})();o&&o!==t&&("home"!==o&&t&&t!==o&&(e.includes(o)||e.includes(t))&&window.location.reload(),t=o)})(...n)),200)}})();wp?.data?.subscribe((()=>{o()}))}));