(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,n=window.wp.components,s=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-course","title":"Instructor Course","category":"learnpress-course-elements","icon":"media-text","description":"Renders template Instructor Course PHP templates.","textdomain":"learnpress","keywords":["instructor course single","learnpress"],"ancestor":["learnpress/single-instructor"],"attributes":{"hidden":{"type":"string","default":""}},"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}');(0,window.wp.blocks.registerBlockType)(s.name,{...s,edit:s=>{const l=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.InspectorControls,null,(0,e.createElement)(n.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(n.SelectControl,{label:(0,t.__)("Display Modes","learnpress"),value:s.attributes.hidden,options:[{label:"Icon + Number + Text",value:""},{label:"Icon + Number",value:"text"},{label:"Number + Text",value:"icon"}],onChange:e=>s.setAttributes({hidden:e||""})}))),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"wrapper-instructor-total-courses"},s.attributes.hidden&&"icon"===s.attributes.hidden?"":(0,e.createElement)("span",{className:"lp-ico lp-icon-courses"}),(0,e.createElement)("span",{className:"instructor-total-courses"},"99"),s.attributes.hidden&&"text"===s.attributes.hidden?"":(0,e.createElement)("span",null," Courses"))))},save:e=>null})})();