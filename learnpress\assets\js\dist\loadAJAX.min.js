(()=>{"use strict";const e="lp-hidden",t=(e,t={},n={})=>{"function"==typeof n.before&&n.before(),fetch(e,{method:"GET",...t}).then((e=>e.json())).then((e=>{"function"==typeof n.success&&n.success(e)})).catch((e=>{"function"==typeof n.error&&n.error(e)})).finally((()=>{"function"==typeof n.completed&&n.completed()}))},n=(e,t)=>{const n=new URL(e);return Object.keys(t).forEach((e=>{n.searchParams.set(e,t[e])})),n};let o={};"undefined"!=typeof lpDataAdmin?o=lpDataAdmin:"undefined"!=typeof lpData&&(o=lpData);var s;window.lpAJAXG=(()=>{const s=".lp-target",a=(()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e})();return{autoLoadAPIs:()=>{console.log("autoLoadAPIs")},fetchAPI:(e,s,a)=>{const r={headers:{}};0!==parseInt(o.user_id)&&(r.headers["X-WP-Nonce"]=o.nonce),void 0!==s.args.method_request?r.method=s.args.method_request:r.method="POST","POST"===r.method?(r.body=JSON.stringify(s),r.headers["Content-Type"]="application/json"):(s.args=JSON.stringify(s.args),s.callback=JSON.stringify(s.callback),e=n(e,s)),t(e,r,a)},fetchAJAX:(e,s)=>{let a=o.lpAjaxUrl;e.hasOwnProperty("args")&&e.args.hasOwnProperty("id_url")&&(a=n(a,{id_url:e.args.id_url})),o.urlParams.hasOwnProperty("lang")&&(a=n(a,{lang:o.urlParams.lang}));const r=new FormData,d=e.hasOwnProperty("action")?e.action:"load_content_via_ajax";r.append("nonce",o.nonce),r.append("lp-load-ajax",d),r.append("data",JSON.stringify(e));const c={method:"POST",headers:{},body:r};0!==parseInt(o.user_id)&&(c.headers["X-WP-Nonce"]=o.nonce),t(a,c,s)},getElements:()=>{const e=document.querySelectorAll(".lp-load-ajax-element:not(.loaded)");e.length&&e.forEach((e=>{const t=e.querySelector(`${s}`);if(!t)return;const n={...JSON.parse(t.dataset.send)},o=e.querySelector(".loading-first"),a={success:e=>{const{status:n,message:o,data:s}=e;"success"===n?t.innerHTML=s.content:"error"===n&&(t.innerHTML=o)},error:e=>{console.log(e)},completed:()=>{window.lpAJAXG.getElements(),o&&o.remove()}};window.lpAJAXG.fetchAJAX(n,a),e.classList.add("loaded")}))},clickNumberPage:(e,t)=>{const r=t.closest(".page-numbers:not(.disabled)");if(!r)return;const d=r.closest(`${s}`);if(!d)return;const c={...JSON.parse(d.dataset.send)};c.args.hasOwnProperty("paged")||(c.args.paged=1),e.preventDefault(),r.classList.contains("prev")?c.args.paged--:r.classList.contains("next")?c.args.paged++:c.args.paged=r.textContent,d.dataset.send=JSON.stringify(c),o.urlParams.paged=c.args.paged,window.history.pushState({},"",n(a,o.urlParams)),window.lpAJAXG.showHideLoading(d,1);const l=d.getBoundingClientRect().top+window.scrollY-100;window.scrollTo({top:l});const i={success:e=>{const{status:t,message:n,data:o}=e;d.innerHTML=o.content||""},error:e=>{console.log(e)},completed:()=>{window.lpAJAXG.showHideLoading(d,0)}};window.lpAJAXG.fetchAJAX(c,i)},getDataSetCurrent:e=>JSON.parse(e.dataset.send),setDataSetCurrent:(e,t)=>e.dataset.send=JSON.stringify(t),showHideLoading:(t,n)=>{const o=t.closest(`div:not(${s})`).querySelector(".lp-loading-change");o&&((t,n=0)=>{t&&(n?t.classList.remove(e):t.classList.add(e))})(o,n)}}})(),window.lpAJAXG.getElements(),document.addEventListener("click",(function(e){const t=e.target;window.lpAJAXG.clickNumberPage(e,t)})),s=e=>{e.classList.contains("lp-load-ajax-element")&&window.lpAJAXG.getElements()},new MutationObserver((function(e){e.forEach((function(e){e.addedNodes&&e.addedNodes.forEach((function(e){1===e.nodeType&&s(e)}))}))})).observe(document,{childList:!0,subtree:!0}),((e,t)=>{const n=document.querySelector(e);if(n)return void t();const o=new MutationObserver(((n,o)=>{const s=document.querySelector(e);s&&(o.disconnect(),t())}));o.observe(document.documentElement,{childList:!0,subtree:!0})})(".lp-load-ajax-element",(e=>{window.lpAJAXG.getElements()})),document.addEventListener("readystatechange",(e=>{window.lpAJAXG.getElements()})),document.addEventListener("DOMContentLoaded",(()=>{window.lpAJAXG.getElements()}))})();