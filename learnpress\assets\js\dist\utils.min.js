(()=>{var __webpack_modules__={467:()=>{"use strict";const $=window.jQuery||jQuery,serializeJSON=function serializeJSON(path){const isInput=$(this).is("input")||$(this).is("select")||$(this).is("textarea");let unIndexed=isInput?$(this).serializeArray():$(this).find("input, select, textarea").serializeArray(),indexed={},validate=/(\[([a-zA-Z0-9_-]+)?\]?)/g,arrayKeys={},end=!1;if($.each(unIndexed,(function(){const that=this,match=this.name.match(/^([0-9a-zA-Z_-]+)/);if(!match)return;let keys=this.name.match(validate),objPath="indexed['"+match[0]+"']";keys?("object"!=typeof indexed[match[0]]&&(indexed[match[0]]={}),$.each(keys,(function(i,prop){prop=prop.replace(/\]|\[/g,"");let rawPath=objPath.replace(/'|\[|\]/g,""),objExp="",preObjPath=objPath;""==prop?(null==arrayKeys[rawPath]?arrayKeys[rawPath]=0:arrayKeys[rawPath]++,objPath+="['"+arrayKeys[rawPath]+"']"):(isNaN(prop)||(arrayKeys[rawPath]=prop),objPath+="['"+prop+"']");try{i==keys.length-1?(objExp=objPath+"=that.value;",end=!0):(objExp=objPath+"={}",end=!1);const evalString="if( typeof "+objPath+" == 'undefined'){"+objExp+";}else{if(end){if(typeof "+preObjPath+"!='object'){"+preObjPath+"={};}"+objExp+"}}";eval(evalString)}catch(e){console.log("Error:"+e+"\n"+objExp)}}))):indexed[match[0]]=this.value})),path){path="['"+path.replace(".","']['")+"']";const c="try{indexed = indexed"+path+"}catch(ex){console.log(c, ex);}";eval(c)}return indexed},LP_Tooltip=e=>(e=$.extend({},{offset:[0,0]},e||{}),$.each(void 0,(function(){const t=$(this),n=t.data("content");if(!n||void 0!==t.data("LP_Tooltip"))return;let o=null;t.on("mouseenter",(function(s){o=$('<div class="learn-press-tooltip-bubble"/>').html(n).appendTo($("body")).hide();const i=t.offset();if(Array.isArray(e.offset)){const t=e.offset[1],n=e.offset[0];$.isNumeric(n)&&(i.left+=n),$.isNumeric(t)&&(i.top+=t)}o.css({top:i.top,left:i.left}),o.fadeIn()})),t.on("mouseleave",(function(e){o&&o.remove()})),t.data("tooltip",!0)}))),hasEvent=function(e){const t=$(this).data("events");if(void 0===t.LP)return!1;for(i=0;i<t.LP.length;i++)if(t.LP[i].namespace==e)return!0;return!1},dataToJSON=function(){const e={};return $.each(this[0].attributes,(function(){const t=this.name.match(/^data-(.*)/);t&&(e[t[1]]=this.value)})),e},rows=function(){const e=$(this).height(),t=$(this).css("line-height").replace("px","");return $(this).attr({height:e,"line-height":t}),Math.floor(e/parseInt(t))},checkLines=function(e){return this.each((function(){const t=$(this).rows();e.call(this,t)}))},findNext=function(e){const t=$(e),n=this.first(),o=t.index(n),s=t.eq(o+1);return!!s.length&&s},findPrev=function(e){const t=$(e),n=this.first(),o=t.index(n),s=t.eq(o-1);return!!s.length&&s},progress=function(e){return this.each((function(){const t=parseInt(e/100*360),n=$(this);t<180?n.find(".progress-circle").removeClass("gt-50"):n.find(".progress-circle").addClass("gt-50"),n.find(".fill").css({transform:"rotate("+t+"deg)"})}))};$.fn.serializeJSON=serializeJSON,$.fn.LP_Tooltip=LP_Tooltip,$.fn.hasEvent=hasEvent,$.fn.dataToJSON=dataToJSON,$.fn.rows=rows,$.fn.checkLines=checkLines,$.fn.findNext=findNext,$.fn.findPrev=findPrev,$.fn.progress=progress;var __WEBPACK_DEFAULT_EXPORT__={serializeJSON,LP_Tooltip,hasEvent,dataToJSON,rows,checkLines,findNext,findPrev,progress}},7764:()=>{!function(e){function t(t,n){const o=e(t),s=o.attr("data-id")||LP.uniqueId();n=e.extend({event:"hover",autoClose:!0,single:!0,closeInterval:1e3,arrowOffset:null,tipClass:""},n,o.data()),o.attr("data-id",s);let i=o.attr("data-content-tip")||o.html(),a=e('<div class="learn-press-tip-floating">'+i+"</div>"),r=null,c=0,l=!1,p="el"===n.arrowOffset?o.outerWidth()/2:8,d=e("#__"+s);function u(){if(r)return void clearTimeout(r);n.single&&e(".learn-press-tip").not(o).LP("QuickTip","close"),a.appendTo(document.body);const t=o.offset();a.css({top:t.top-a.outerHeight()-8,left:t.left-a.outerWidth()/2+p})}function h(){r&&clearTimeout(r),r=setTimeout((function(){a.detach(),r=null}),c)}function f(){c=0,h(),c=n.closeInterval}return 0===d.length&&e(document.body).append(e("<div />").attr("id","__"+s).html(i).css("display","none")),i=d.html(),a.addClass(n.tipClass),o.data("content-tip",i),o.attr("data-content-tip")&&(l=!0),c=n.closeInterval,!1===n.autoClose&&(a.append('<a class="close"></a>'),a.on("click",".close",(function(){f()}))),l||o.html(""),"click"===n.event&&o.on("click",(function(e){e.stopPropagation(),u()})),e(document).on("learn-press/close-all-quick-tip",(function(){f()})),o.hover((function(e){e.stopPropagation(),"click"!==n.event&&u()}),(function(e){e.stopPropagation(),n.autoClose&&h()})).addClass("ready"),{close:f,open:function(){u()}}}e.fn.LP("QuickTip",(function(n){return e.each(this,(function(){let o=e(this).data("quick-tip");o||(o=new t(this,n),e(this).data("quick-tip",o)),"string"==typeof n&&o[n]&&o[n].apply(o)}))}))}(jQuery)},3310:()=>{const e=jQuery;e((function(){e('.form-field input[type="password"]').wrap('<span class="lp-password-input"></span>'),e(".lp-password-input").append('<span class="lp-show-password-input"></span>'),e(".lp-show-password-input").on("click",(function(){e(this).toggleClass("display-password"),e(this).hasClass("display-password")?e(this).siblings(['input[type="password"]']).prop("type","text"):e(this).siblings('input[type="text"]').prop("type","password")}))}))}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.jQuery;let t;void 0!==e&&(e.fn.LP=t=function(t,n){if("function"==typeof n)e.fn["LP_"+t]=n;else if(t){const n=[];if(arguments.length>1)for(let e=1;e<arguments.length;e++)n.push(arguments[e]);return"function"==typeof e(this)["LP_"+t]?e(this)["LP_"+t].apply(this,n):this}return this});var n=__webpack_require__(7764),o=__webpack_require__.n(n);const s=window.jQuery,i={$block:null,$window:null,events:{},instances:[],instance:null,quickConfirm(e,t){const n=s(e);s("[learn-press-quick-confirm]").each((function(){let e;(e=s(this).data("quick-confirm"))&&(console.log(e),e.destroy())})),!n.attr("learn-press-quick-confirm")&&n.attr("learn-press-quick-confirm","true").data("quick-confirm",new function(e,t){var n=s(e),o=s('<span class="learn-press-quick-confirm"></span>').insertAfter(n),i=s(e).position()||{left:0,top:0},a=null,r=null,c=3,l=function(){o.fadeOut("fast",(function(){s(this).remove(),o.parent().css("position","")})),n.removeAttr("learn-press-quick-confirm").data("quick-confirm",void 0),p()},p=function(){r&&clearInterval(r),a&&clearInterval(a)},d=function(){a=setInterval((function(){0==--c&&(l.call(o[0]),"function"==typeof t.onCancel&&t.onCancel(t.data),p()),o.find("span").html(" ("+c+")")}),1e3),r=setInterval((function(){n.is(":visible")&&"hidden"!=n.css("visibility")||(p(),o.remove(),o.parent().css("position",""),"function"==typeof t.onCancel&&t.onCancel(t.data))}),350)};t=s.extend({message:"",data:null,onOk:null,onCancel:null,offset:{top:0,left:0}},t||{}),o.html(t.message||n.attr("data-confirm-remove")||"Are you sure?").append("<span> ("+c+")</span>").css({}),o.click((function(){"function"==typeof t.onOk&&t.onOk(t.data),l()})).hover((function(){p()}),(function(){d()})),o.css({left:i.left+n.outerWidth()-o.outerWidth()+t.offset.left,top:i.top+n.outerHeight()+t.offset.top+5}).hide().fadeIn("fast"),d(),this.destroy=function(){o.remove(),n.removeAttr("learn-press-quick-confirm").data("quick-confirm",void 0),p()}}(e,t))},show(e,t){s.proxy((function(){t=s.extend({title:"",buttons:"",events:!1,autohide:!1,message:e,data:!1,id:LP.uniqueId(),onHide:null},t||{}),this.instances.push(t),this.instance=t,s(document);const n=s(document.body);this.$block||(this.$block=s('<div id="learn-press-message-box-block"></div>').appendTo(n)),this.$window||(this.$window=s('<div id="learn-press-message-box-window"><div id="message-box-wrap"></div> </div>').insertAfter(this.$block),this.$window.click((function(){}))),this._createWindow(e,t.title,t.buttons),this.$block.show(),this.$window.show().attr("instance",t.id),s(window).bind("resize.message-box",s.proxy(this.update,this)).bind("scroll.message-box",s.proxy(this.update,this)),this.update(!0),t.autohide&&setTimeout((function(){LP.MessageBox.hide(),"function"==typeof t.onHide&&t.onHide.call(LP.MessageBox,t)}),t.autohide)}),this)()},blockUI(e){e=(!1!==e?e||"Wait a moment":"")+'<div class="message-box-animation"></div>',this.show(e)},hide(e,t){t?this._removeInstance(t.id):this.instance&&this._removeInstance(this.instance.id),0===this.instances.length?(this.$block&&this.$block.hide(),this.$window&&this.$window.hide(),s(window).unbind("resize.message-box",this.update).unbind("scroll.message-box",this.update)):this.instance&&this._createWindow(this.instance.message,this.instance.title,this.instance.buttons)},update(e){let t=this,n=this.$window.find("#message-box-wrap"),o=n.data("timer"),i=function(){LP.Hook.doAction("learn_press_message_box_before_resize",t);let e=n.find(".message-box-content").css("height","").css("overflow","hidden"),o=(n.outerWidth(),n.outerHeight()),i=e.height(),a=s(window).height();n.offset().top,i>a-50?(e.css({height:a-25}),o=n.outerHeight()):e.css("height","").css("overflow",""),n.css({marginTop:(s(window).height()-o)/2}),LP.Hook.doAction("learn_press_message_box_resize",o,t)};e&&i(),o&&clearTimeout(o),o=setTimeout(i,250)},_removeInstance(e){for(let t=0;t<this.instances.length;t++)if(this.instances[t].id===e){this.instances.splice(t,1);const e=this.instances.length;e?(this.instance=this.instances[e-1],this.$window.attr("instance",this.instance.id)):(this.instance=!1,this.$window.removeAttr("instance"));break}},_getInstance(e){for(let t=0;t<this.instances.length;t++)if(this.instances[t].id===e)return this.instances[t]},_createWindow(e,t,n){const o=this.$window.find("#message-box-wrap").html("");if(t&&o.append('<h3 class="message-box-title">'+t+"</h3>"),o.append(s('<div class="message-box-content"></div>').html(e)),n){const e=s('<div class="message-box-buttons"></div>');switch(n){case"yesNo":e.append(this._createButton(LP_Settings.localize.button_yes,"yes")),e.append(this._createButton(LP_Settings.localize.button_no,"no"));break;case"okCancel":e.append(this._createButton(LP_Settings.localize.button_ok,"ok")),e.append(this._createButton(LP_Settings.localize.button_cancel,"cancel"));break;default:e.append(this._createButton(LP_Settings.localize.button_ok,"ok"))}o.append(e)}},_createButton(e,t){const n=s('<button type="button" class="button message-box-button message-box-button-'+t+'">'+e+"</button>"),o="on"+(t.substr(0,1).toUpperCase()+t.substr(1));return n.data("callback",o).click((function(){const e=s(this).data("instance"),t=e.events[s(this).data("callback")];"function"===s.type(t)&&!1===t.apply(LP.MessageBox,[e])||LP.MessageBox.hide(null,e)})).data("instance",this.instance),n}},a={hooks:{action:{},filter:{}},addAction(e,t,n,o){return this.addHook("action",e,t,n,o),this},addFilter(e,t,n,o){return this.addHook("filter",e,t,n,o),this},doAction(e){return this.doHook("action",e,arguments)},applyFilters(e){return this.doHook("filter",e,arguments)},removeAction(e,t){return this.removeHook("action",e,t),this},removeFilter(e,t,n){return this.removeHook("filter",e,t,n),this},addHook(e,t,n,o,s){void 0===this.hooks[e][t]&&(this.hooks[e][t]=[]);const i=this.hooks[e][t];return void 0===s&&(s=t+"_"+i.length),this.hooks[e][t].push({tag:s,callable:n,priority:o}),this},doHook(e,t,n){if(n=Array.prototype.slice.call(n,1),void 0!==this.hooks[e][t]){let o,s=this.hooks[e][t];s.sort((function(e,t){return e.priority-t.priority}));for(let t=0;t<s.length;t++)o=s[t].callable,"function"!=typeof o&&(o=window[o]),"action"===e?n[t]=o.apply(null,n):n[0]=o.apply(null,n)}return"filter"===e?n[0]:n},removeHook(e,t,n,o){if(void 0!==this.hooks[e][t]){const s=this.hooks[e][t];for(let e=s.length-1;e>=0;e--)void 0!==o&&o!==s[e].tag||void 0!==n&&n!==s[e].priority||s.splice(e,1)}return this}},r=a,c={get:(e,t,n)=>{let o;if(n)o=wpCookies.get(e);else{let t=wpCookies.get("LP");t&&(t=JSON.parse(t),o=e?t[e]:t)}return o||o===t||(o=t),o},set(e,t,n,o,s,i){if(arguments.length>2)wpCookies.set(e,t,n,o,s,i);else if(2==arguments.length){let n=wpCookies.get("LP");n=n?JSON.parse(n):{},n[e]=t,wpCookies.set("LP",JSON.stringify(n),"","/")}else wpCookies.set("LP",JSON.stringify(e),"","/")},remove(e){const t=c.get(),n=new RegExp(e,"g"),o={},s=e.match(/\*/);for(const i in t)s?i.match(n)||(o[i]=t[i]):e!=i&&(o[i]=t[i]);c.set(o)}},l=c,p={__key:"LP",set(e,t){const n=p.get(),{set:o}=lodash;o(n,e,t),localStorage.setItem(p.__key,JSON.stringify(n))},get(e,t){const n=JSON.parse(localStorage.getItem(p.__key)||"{}"),{get:o}=lodash,s=o(n,e);return e?void 0!==s?s:t:n},exists:e=>e in p.get(),remove(e){const t=p.get(),n=lodash.omit(t,e);p.__set(n)},__get:()=>localStorage.getItem(p.__key),__set(e){localStorage.setItem(p.__key,JSON.stringify(e||"{}"))}},d=p;__webpack_require__(467);let u=1;const h=window.jQuery||jQuery;var f=__webpack_require__(3310),g=__webpack_require__.n(f);const m=jQuery;String.prototype.getQueryVar=function(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");const t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(this);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))},String.prototype.addQueryVar=function(e,t){let n=this,o=n.split("#");return n=o[0],e.match(/\[/)?(n+=n.match(/\?/)?"&":"?",n+=e+"="+t):-1!=n.indexOf("&"+e+"=")||-1!=n.indexOf("?"+e+"=")?n=n.replace(new RegExp(e+"=([^&#]*)","g"),e+"="+t):(n+=n.match(/\?/)?"&":"?",n+=e+"="+t),n+(o[1]?"#"+o[1]:"")},String.prototype.removeQueryVar=function(e){let t=this;const n=t.split("#");t=n[0],e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");const o=new RegExp("[\\?&]"+e+"([[][^=]*)?=([^&#]*)","g");return t=t.replace(o,""),t+(n[1]?"#"+n[1]:"")};const y={Hook:r,setUrl(e,t,n){e&&(history.pushState({},n,e),LP.Hook.doAction("learn_press_set_location_url",e))},toggleGroupSection(e,t){const n=m(e);n.hasClass("hide-if-js")&&n.hide().removeClass("hide-if-js"),n.removeClass("hide-if-js").slideToggle((function(){m(this).is(":visible")?m(t).addClass("toggle-on").removeClass("toggle-off"):m(t).addClass("toggle-off").removeClass("toggle-on")}))},overflow(e,t){const n=m(e),o=n.css("overflow");t?n.css("overflow",t).data("overflow",o):n.css("overflow",n.data("overflow"))},getUrl:()=>window.location.href,addQueryVar:(e,t,n)=>(void 0===n?window.location.href:n).addQueryVar(e,t),removeQueryVar:(e,t)=>(void 0===t?window.location.href:t).removeQueryVar(e),reload(e){e||(e=window.location.href),window.location.href=e},parseResponse(e,t){const n=e.match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);return n&&(e=n[1]),"json"===(t||"json")?this.parseJSON(e):e},parseJSON(e){if("string"!=typeof e)return e;const t=String.raw({raw:e}).match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);try{e=t?JSON.parse(t[1].replace(/(?:\r\n|\r|\n)/g,"")):JSON.parse(e)}catch(t){e={}}return e},ajax(e){const t=e.type||"post",n=e.dataType||"json",o=e.action?m.extend(e.data,{"lp-ajax":e.action}):e.data,s=e.beforeSend||function(){},i=e.url||window.location.href;m.ajax({data:o,url:i,type:t,dataType:"html",beforeSend:s.apply(null,e),success(t){const o=LP.parseResponse(t,n);"function"==typeof e.success&&e.success(o,t)},error(){"function"==typeof e.error&&e.error.apply(null,LP.funcArgs2Array())}})},doAjax(e){const t=e.type||"post",n=e.dataType||"json",o=(void 0===e.prefix||"learnpress_")+e.action,s=e.action?m.extend(e.data,{action:o}):e.data;m.ajax({data:s,url:e.url||window.location.href,type:t,dataType:"html",success(t){const o=LP.parseResponse(t,n);"function"==typeof e.success&&e.success(o,t)},error(){"function"==typeof e.error&&e.error.apply(null,LP.funcArgs2Array())}})},funcArgs2Array(e){const t=[];for(let n=0;n<e.length;n++)t.push(e[n]);return t},addFilter(e,t){const n=m(document),o="LP."+e;return n.on(o,t),LP.log(n.data("events")),this},applyFilters(){const e=m(document),t=arguments[0],n=this.funcArgs2Array(arguments);return e.hasEvent(t)?(n[0]="LP."+t,e.triggerHandler.apply(e,n)):n[1]},addAction(e,t){return this.addFilter(e,t)},doAction(){const e=m(document),t=arguments[0],n=this.funcArgs2Array(arguments);e.hasEvent(t)&&(n[0]="LP."+t,e.trigger.apply(e,n))},toElement(e,t){if(0===m(e).length)return;t=m.extend({delay:300,duration:"slow",offset:50,container:null,callback:null,invisible:!1},t||{});let n=m(t.container),o=0;0===n.length&&(n=m("body, html")),o=n.offset().top;const s=m(e).offset().top+n.scrollTop()-o-t.offset;t.invisible&&function(e){const t=n.scrollTop(),o=t+n.height(),s=m(e).offset().top-n.offset().top,i=s+m(e).height();return t<s&&o>i}(e)||n.fadeIn(10).delay(t.delay).animate({scrollTop:s},t.duration,t.callback)},uniqueId(e,t){let n;void 0===e&&(e="");const o=function(e,t){return t<(e=parseInt(e,10).toString(16)).length?e.slice(e.length-t):t>e.length?new Array(t-e.length+1).join("0")+e:e};return this.php_js||(this.php_js={}),this.php_js.uniqidSeed||(this.php_js.uniqidSeed=Math.floor(123456789*Math.random())),this.php_js.uniqidSeed++,n=e,n+=o(parseInt((new Date).getTime()/1e3,10),8),n+=o(this.php_js.uniqidSeed,5),t&&(n+=(10*Math.random()).toFixed(8).toString()),n},log(){for(let e=0,t=arguments.length;e<t;e++)console.log(arguments[e])},blockContent(){0===m("#learn-press-block-content").length&&m(LP.template("learn-press-template-block-content",{})).appendTo(m("body")),LP.hideMainScrollbar().addClass("block-content"),m(document).trigger("learn_press_block_content")},unblockContent(){setTimeout((function(){LP.showMainScrollbar().removeClass("block-content"),m(document).trigger("learn_press_unblock_content")}),350)},hideMainScrollbar(e){e||(e="html, body");const t=m(e);return t.each((function(){const e=m(this),t=e.css("overflow");e.css("overflow","hidden").attr("overflow",t)})),t},showMainScrollbar(e){e||(e="html, body");const t=m(e);return t.each((function(){const e=m(this),t=e.attr("overflow");e.css("overflow",t).removeAttr("overflow")})),t},template:"undefined"!=typeof _?_.memoize((function(e,t){let n,o={evaluate:/<#([\s\S]+?)#>/g,interpolate:/\{\{\{([\s\S]+?)\}\}\}/g,escape:/\{\{([^\}]+?)\}\}(?!\})/g,variable:"data"};const s=function(t){return n=n||_.template(m("#"+e).html(),null,o),n(t)};return t?s(t):s}),(function(e,t){return e+"-"+JSON.stringify(t)})):function(){return""},alert(e,t){let n="",o="";"string"==typeof e?o=e:(void 0!==e.title&&(n=e.title),void 0!==e.message&&(o=e.message)),m.alerts.alert(o,n,(function(e){LP._on_alert_hide(),t&&t(e)})),this._on_alert_show()},confirm(e,t){let n="",o="";"string"==typeof e?o=e:(void 0!==e.title&&(n=e.title),void 0!==e.message&&(o=e.message)),m.alerts.confirm(o,n,(function(e){LP._on_alert_hide(),t&&t(e)})),this._on_alert_show()},_on_alert_show(){const e=m("#popup_container");m('<span id="popup_container_placeholder" />').insertAfter(e).data("xxx",e),e.stop().css("top","-=50").css("opacity","0").animate({top:"+=50",opacity:1},250)},_on_alert_hide(){const e=m("#popup_container_placeholder"),t=e.data("xxx");t&&t.replaceWith(e),t.appendTo(m(document.body)),t.stop().animate({top:"+=50",opacity:0},250,(function(){m(this).remove()}))},sendMessage(e,t,n,o){m.isPlainObject(e)&&(e=JSON.stringify(e)),n=n||"*",(t=t||window).postMessage(e,n,o)},receiveMessage(e,t){let n=e.origin||e.originalEvent.origin,o=e.data||e.originalEvent.data||"";("string"==typeof o||o instanceof String)&&0===o.indexOf("{")&&(o=LP.parseJSON(o)),LP.Hook.doAction("learn_press_receive_message",o,n)},camelCaseDashObjectKeys(e,t=!0){const n=LP,o=function(e){return Array.isArray(e)};if((s=e)===Object(s)&&!o(s)&&"function"!=typeof s){const o={};return Object.keys(e).forEach((s=>{var i;o[(i=s,i.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace("-","").replace("_",""))))]=t?n.camelCaseDashObjectKeys(e[s]):e[s]})),o}return o(e)?e.map((e=>n.camelCaseDashObjectKeys(e))):e;var s},IframeSubmit:function(e){const t="ajax-iframe-"+u;let n=h('form[name="'+t+'"]');n.length||(n=h("<iframe />").appendTo(document.body).attr({name:t,src:"#"})),h(e).on("submit",(function(){const n=h(e).clone().appendTo(document.body);return n.attr("target",t),n.find("#submit").remove(),!1})),u++}};m(document).ready((function(){void 0!==m.alerts&&(m.alerts.overlayColor="#000",m.alerts.overlayOpacity=.5,m.alerts.okButton=lpGlobalSettings.localize.button_ok,m.alerts.cancelButton=lpGlobalSettings.localize.button_cancel),m(".learn-press-message.fixed").each((function(){const e=m(this),t=e.data();!function(e,t){t.delayIn&&setTimeout((function(){e.show().hide().fadeIn()}),t.delayIn),t.delayOut&&setTimeout((function(){e.fadeOut()}),t.delayOut+(t.delayIn||0))}(e,t)})),setTimeout((function(){m(".learn-press-nav-tabs li.active:not(.default) a").trigger("click")}),300),function(){let e=null,t=function(){m(".auto-check-lines").checkLines((function(e){e>1?m(this).removeClass("single-lines"):m(this).addClass("single-lines"),m(this).attr("rows",e)}))};m(window).on("resize.check-lines",(function(){e?(e&&clearTimeout(e),e=setTimeout(t,300)):t()}))}(),m(".learn-press-tooltip, .lp-passing-conditional").LP_Tooltip({offset:[24,24]}),m(".learn-press-icon").LP_Tooltip({offset:[30,30]}),m(".learn-press-message[data-autoclose]").each((function(){const e=m(this),t=parseInt(e.data("autoclose"));t&&setTimeout((function(e){e.fadeOut()}),t,e)})),m(document).on("click",(function(){m(document).trigger("learn-press/close-all-quick-tip")}))})),function(){window.LP=window.LP||{},"string"==typeof arguments[0]?(LP[arguments[0]]=LP[arguments[0]]||{},LP[arguments[0]]=jQuery.extend(LP[arguments[0]],arguments[1])):LP=jQuery.extend(LP,arguments[0])}({Event_Callback:function(e){const t={};window.jQuery,this.on=function(n,o){let s=n.split("."),i="";return s.length>1&&(n=s[0],i=s[1]),t[n]||(t[n]=[[],{}]),i?(t[n][1][i]||(t[n][1][i]=[]),t[n][1][i].push(o)):t[n][0].push(o),e},this.off=function(n,o){let s=n.split("."),i="";if(s.length>1&&(n=s[0],i=s[1]),!t[n])return e;let a=-1;if(i){if(!t[n][1][i])return e;if("function"==typeof o){if(a=t[n][1][i].indexOf(o),a<0)return e;t[n][1][i].splice(a,1)}else t[n][1][i]=[]}else if("function"==typeof o){if(a=t[n][0].indexOf(o),a<0)return e;t[n][0].splice(a,1)}else t[n][0]=[];return e},this.callEvent=function(n,o){if(t[n]){if(t[n][0])for(var s=0;s<t[n][0].length;s++)"function"==typeof t[n][0][s]&&t[n][s][0].apply(e,o);if(t[n][1])for(var s in t[n][1])for(let i=0;i<t[n][1][s].length;i++)"function"==typeof t[n][1][s][i]&&t[n][1][s][i].apply(e,o)}}},MessageBox:i,Cookies:l,localStorage:d,...y}),o(),g()})()})();