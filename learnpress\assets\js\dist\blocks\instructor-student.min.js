(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,s=window.wp.components,r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/instructor-student","title":"Instructor Student","category":"learnpress-course-elements","icon":"admin-users","description":"Renders template Instructor Student PHP templates.","textdomain":"learnpress","keywords":["instructor student single","learnpress"],"ancestor":["learnpress/single-instructor"],"usesContext":[],"attributes":{"hidden":{"type":"string","default":""}},"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}}}}');(0,window.wp.blocks.registerBlockType)(r.name,{...r,edit:r=>{const l=(0,n.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.PanelBody,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(s.SelectControl,{label:(0,t.__)("Display Modes","learnpress"),value:r.attributes.hidden,options:[{label:"Icon + Number + Text",value:""},{label:"Icon + Number",value:"text"},{label:"Number + Text",value:"icon"}],onChange:e=>r.setAttributes({hidden:e||""})}))),(0,e.createElement)("div",{...l},(0,e.createElement)("div",{className:"wrapper-instructor-total-students"},r.attributes.hidden&&"icon"===r.attributes.hidden?"":(0,e.createElement)("span",{className:"lp-ico lp-icon-students"}),(0,e.createElement)("span",{className:"instructor-total-students"},"99"),r.attributes.hidden&&"text"===r.attributes.hidden?"":(0,e.createElement)("span",null," Students"))))},save:e=>null})})();