/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/src/apps/js/admin/editor/actions/course-section.js":
/*!*******************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/actions/course-section.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const $ = window.jQuery || jQuery;
const CourseCurriculum = {
  toggleAllSections(context) {
    const hidden = context.getters.isHiddenAllSections;
    if (hidden) {
      context.commit('OPEN_ALL_SECTIONS');
    } else {
      context.commit('CLOSE_ALL_SECTIONS');
    }
    LP.Request({
      type: 'hidden-sections',
      hidden: context.getters.hiddenSections
    });
  },
  updateSectionsOrder(context, order) {
    LP.Request({
      type: 'sort-sections',
      order: JSON.stringify(order)
    }).then(function (response) {
      const result = response.body;
      const order_sections = result.data;
      context.commit('SORT_SECTION', order_sections);
    }, function (error) {
      console.error(error);
    });
  },
  toggleSection(context, section) {
    if (section.open) {
      context.commit('CLOSE_SECTION', section);
    } else {
      context.commit('OPEN_SECTION', section);
    }
    LP.Request({
      type: 'hidden-sections',
      hidden: context.getters.hiddenSections
    });
  },
  updateSection(context, section) {
    context.commit('UPDATE_SECTION_REQUEST', section.id);
    LP.Request({
      type: 'update-section',
      section: JSON.stringify(section)
    }).then(function () {
      context.commit('UPDATE_SECTION_SUCCESS', section.id);
    }).catch(function () {
      context.commit('UPDATE_SECTION_FAILURE', section.id);
    });
  },
  removeSection(context, payload) {
    context.commit('REMOVE_SECTION', payload.index);
    LP.Request({
      type: 'remove-section',
      section_id: payload.section.id
    }).then(function (response) {
      const result = response.body;
    }, function (error) {
      console.error(error);
    });
  },
  newSection(context, name) {
    const newSection = {
      type: 'new-section',
      section_name: name,
      temp_id: LP.uniqueId()
    };
    context.commit('ADD_NEW_SECTION', {
      id: newSection.temp_id,
      items: [],
      open: false,
      title: newSection.section_name
    });
    LP.Request(newSection).then(function (response) {
      const result = response.body;
      if (result.success) {
        const section = $.extend({}, result.data, {
          open: true
        });
        context.commit('ADD_NEW_SECTION', section);
      }
    }, function (error) {
      console.error(error);
    });
  },
  updateSectionItem(context, payload) {
    context.commit('UPDATE_SECTION_ITEM_REQUEST', payload.item.id);
    LP.Request({
      type: 'update-section-item',
      section_id: payload.section_id,
      item: JSON.stringify(payload.item)
    }).then(function (response) {
      context.commit('UPDATE_SECTION_ITEM_SUCCESS', payload.item.id);
      const result = response.body;
      if (result.success) {
        const item = result.data;
        context.commit('UPDATE_SECTION_ITEM', {
          section_id: payload.section_id,
          item
        });
      }
    }, function (error) {
      context.commit('UPDATE_SECTION_ITEM_FAILURE', payload.item.id);
      console.error(error);
    });
  },
  removeSectionItem(context, payload) {
    const id = payload.item.id;
    context.commit('REMOVE_SECTION_ITEM', payload);
    payload.item.temp_id = 0;
    LP.Request({
      type: 'remove-section-item',
      section_id: payload.section_id,
      item_id: id
    }).then(function (rs) {
      const {
        data,
        success
      } = rs.body;
      if (success) {
        context.commit('REMOVE_SECTION_ITEM', payload);
      } else {
        alert(data);
        payload.oldId = id;
        context.commit('REMOVE_SECTION_ITEM', payload);
      }
      context.commit('REMOVE_SECTION_ITEM', payload);
    });
  },
  deleteSectionItem(context, payload) {
    const id = payload.item.id;
    context.commit('REMOVE_SECTION_ITEM', payload);
    payload.item.temp_id = 0;
    LP.Request({
      type: 'delete-section-item',
      section_id: payload.section_id,
      item_id: id
    }).then(function (rs) {
      const {
        data,
        success
      } = rs.body;
      if (success) {
        context.commit('REMOVE_SECTION_ITEM', payload);
      } else {
        alert(data);
        payload.oldId = id;
        context.commit('REMOVE_SECTION_ITEM', payload);
      }
    });
  },
  newSectionItem(context, payload) {
    context.commit('APPEND_EMPTY_ITEM_TO_SECTION', payload);
    //context.commit('UPDATE_SECTION_ITEMS', {section_id: payload.section_id, items: result.data});
    LP.Request({
      type: 'new-section-item',
      section_id: payload.section_id,
      item: JSON.stringify(payload.item)
    }).then(function (response) {
      const result = response.body;
      if (result.success) {
        // context.commit('UPDATE_SECTION_ITEMS', {section_id: payload.section_id, items: result.data});
        const items = {};
        $.each(result.data, function (i, a) {
          items[a.old_id ? a.old_id : a.id] = a;
        });
        context.commit('UPDATE_ITEM_SECTION_BY_ID', {
          section_id: payload.section_id,
          items
        });
      }
    }, function (error) {
      console.error(error);
    });
  },
  updateSectionItems({
    state
  }, payload) {
    LP.Request({
      type: 'update-section-items',
      section_id: payload.section_id,
      items: JSON.stringify(payload.items),
      last_section: state.sections[state.sections.length - 1] === payload.section_id
    }).then(function (response) {
      const result = response.body;
      if (result.success) {
        // console.log(result);
      }
    }, function (error) {
      console.error(error);
    });
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseCurriculum);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/actions/course.js":
/*!***********************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/actions/course.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Course = {
  heartbeat: function (context) {
    LP.Request({
      type: 'heartbeat'
    }).then(function (response) {
      var result = response.body;
      context.commit('UPDATE_HEART_BEAT', !!result.success);
    }, function (error) {
      context.commit('UPDATE_HEART_BEAT', false);
    });
  },
  draftCourse: function (context, payload) {
    var auto_draft = context.getters.autoDraft;
    if (auto_draft) {
      LP.Request({
        type: 'draft-course',
        course: JSON.stringify(payload)
      }).then(function (response) {
        var result = response.body;
        if (!result.success) {
          return;
        }
        context.commit('UPDATE_AUTO_DRAFT_STATUS', false);
      });
    }
  },
  newRequest: function (context) {
    context.commit('INCREASE_NUMBER_REQUEST');
    context.commit('UPDATE_STATUS', 'loading');
    window.onbeforeunload = function () {
      return '';
    };
  },
  requestCompleted: function (context, status) {
    context.commit('DECREASE_NUMBER_REQUEST');
    if (context.getters.currentRequest === 0) {
      context.commit('UPDATE_STATUS', status);
      window.onbeforeunload = null;
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Course);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/actions/modal-course-items.js":
/*!***********************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/actions/modal-course-items.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const ModalCourseItems = {
  toggle: function (context) {
    context.commit('TOGGLE');
  },
  open: function (context, sectionId) {
    context.commit('SET_SECTION', sectionId);
    context.commit('RESET');
    context.commit('TOGGLE');
  },
  searchItems: function (context, payload) {
    context.commit('SEARCH_ITEMS_REQUEST');
    LP.Request({
      type: 'search-items',
      query: payload.query,
      item_type: payload.type,
      page: payload.page,
      exclude: JSON.stringify([])
    }).then(function (response) {
      var result = response.body;
      if (!result.success) {
        return;
      }
      var data = result.data;
      context.commit('SET_LIST_ITEMS', data.items);
      context.commit('UPDATE_PAGINATION', data.pagination);
      context.commit('SEARCH_ITEMS_SUCCESS');
    }, function (error) {
      context.commit('SEARCH_ITEMS_FAILURE');
      console.error(error);
    });
  },
  addItem: function (context, item) {
    context.commit('ADD_ITEM', item);
  },
  removeItem: function (context, index) {
    context.commit('REMOVE_ADDED_ITEM', index);
  },
  addItemsToSection: function (context) {
    var items = context.getters.addedItems;
    if (items.length > 0) {
      LP.Request({
        type: 'add-items-to-section',
        section_id: context.getters.section,
        items: JSON.stringify(items)
      }).then(function (response) {
        var result = response.body;
        if (result.success) {
          context.commit('TOGGLE');
          var items = result.data;
          context.commit('ss/UPDATE_SECTION_ITEMS', {
            section_id: context.getters.section,
            items: items
          }, {
            root: true
          });
        }
      }, function (error) {
        console.error(error);
      });
    }
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModalCourseItems);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/getters/course-section.js":
/*!*******************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/getters/course-section.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const CourseCurriculum = {
  sections: function (state) {
    return state.sections || [];
  },
  urlEdit: function (state) {
    return state.urlEdit;
  },
  hiddenSections: function (state) {
    return state.sections.filter(function (section) {
      return !section.open;
    }).map(function (section) {
      return parseInt(section.id);
    });
  },
  isHiddenAllSections: function (state, getters) {
    var sections = getters.sections;
    var hiddenSections = getters.hiddenSections;
    return hiddenSections.length === sections.length;
  },
  statusUpdateSection: function (state) {
    return state.statusUpdateSection;
  },
  statusUpdateSectionItem: function (state) {
    return state.statusUpdateSectionItem;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseCurriculum);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/getters/course.js":
/*!***********************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/getters/course.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Course = {
  heartbeat: function (state) {
    return state.heartbeat;
  },
  action: function (state) {
    return state.action;
  },
  id: function (state) {
    return state.course_id;
  },
  autoDraft: function (state) {
    return state.auto_draft;
  },
  disable_curriculum: function (state) {
    return state.disable_curriculum;
  },
  status: function (state) {
    return state.status || 'error';
  },
  currentRequest: function (state) {
    return state.countCurrentRequest || 0;
  },
  urlAjax: function (state) {
    return state.ajax;
  },
  nonce: function (state) {
    return state.nonce;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Course);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/getters/modal-course-items.js":
/*!***********************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/getters/modal-course-items.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Getters = {
  status: function (state) {
    return state.status;
  },
  pagination: function (state) {
    return state.pagination;
  },
  items: function (state, _getters) {
    return state.items.map(function (item) {
      var find = _getters.addedItems.find(function (_item) {
        return item.id === _item.id;
      });
      item.added = !!find;
      return item;
    });
  },
  addedItems: function (state) {
    return state.addedItems;
  },
  isOpen: function (state) {
    return state.open;
  },
  types: function (state) {
    return state.types;
  },
  section: function (state) {
    return state.sectionId;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Getters);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/http.js":
/*!*************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/http.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ HTTP)
/* harmony export */ });
function HTTP(options) {
  const $ = window.jQuery || jQuery;
  const $VueHTTP = Vue.http;
  options = $.extend({
    ns: 'LPRequest',
    store: false
  }, options || {});
  let $publishingAction = null;
  LP.Request = function (payload) {
    $publishingAction = $('#publishing-action');
    payload.id = options.store.getters.id;
    payload.nonce = options.store.getters.nonce;
    payload['lp-ajax'] = options.store.getters.action;
    //payload.code = options.store.getters.code;

    $publishingAction.find('#publish').addClass('disabled');
    $publishingAction.find('.spinner').addClass('is-active');
    $publishingAction.addClass('code-' + payload.code);
    return $VueHTTP.post(options.store.getters.urlAjax, payload, {
      emulateJSON: true,
      params: {
        namespace: options.ns,
        code: payload.code
      }
    });
  };
  $VueHTTP.interceptors.push(function (request, next) {
    if (request.params.namespace !== options.ns) {
      next();
      return;
    }
    options.store.dispatch('newRequest');
    next(function (response) {
      if (!jQuery.isPlainObject(response.body)) {
        response.body = LP.parseJSON(response.body);
      }
      const body = response.body;
      const result = body.success || false;
      if (result) {
        options.store.dispatch('requestCompleted', 'successful');
      } else {
        options.store.dispatch('requestCompleted', 'failed');
      }
      $publishingAction.removeClass('code-' + request.params.code);
      if (!$publishingAction.attr('class')) {
        $publishingAction.find('#publish').removeClass('disabled');
        $publishingAction.find('.spinner').removeClass('is-active');
      }
    });
  });
}

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/mutations/course-section.js":
/*!*********************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/mutations/course-section.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const CourseCurriculum = {
  SORT_SECTION(state, orders) {
    state.sections = state.sections.map(function (section) {
      section.order = orders[section.id];
      return section;
    });
  },
  SET_SECTIONS(state, sections) {
    state.sections = sections;
  },
  ADD_NEW_SECTION(state, newSection) {
    if (newSection.open === undefined) {
      newSection.open = true;
    }
    let pos;
    if (newSection.temp_id) {
      state.sections.map(function (section, i) {
        if (newSection.temp_id == section.id) {
          pos = i;
          return false;
        }
      });
    }
    if (pos !== undefined) {
      $Vue.set(state.sections, pos, newSection);
    } else {
      state.sections.push(newSection);
    }
  },
  ADD_EMPTY_SECTION(state, section) {
    section.open = true;
    state.sections.push(section);
  },
  REMOVE_SECTION(state, index) {
    state.sections.splice(index, 1);
  },
  REMOVE_SECTION_ITEM(state, payload) {
    const section = state.sections.find(function (section) {
      return section.id === payload.section_id;
    });
    let items = section.items || [],
      item = payload.item,
      index = -1;
    items.forEach(function (it, i) {
      if (it.id === item.id) {
        index = i;
      }
    });
    if (index !== -1) {
      if (payload.oldId !== undefined) {
        items[index].id = payload.oldId;
        return;
      }
      if (item.temp_id) {
        items[index].id = item.temp_id;
      } else {
        items.splice(index, 1);
      }
    }
  },
  UPDATE_SECTION_ITEMS(state, payload) {
    const section = state.sections.find(function (section) {
      return parseInt(section.id) === parseInt(payload.section_id);
    });
    if (!section) {
      return;
    }
    section.items = payload.items;
  },
  UPDATE_SECTION_ITEM(state, payload) {},
  CLOSE_SECTION(state, section) {
    state.sections.forEach(function (_section, index) {
      if (section.id === _section.id) {
        state.sections[index].open = false;
      }
    });
  },
  OPEN_SECTION(state, section) {
    state.sections.forEach(function (_section, index) {
      if (section.id === _section.id) {
        state.sections[index].open = true;
      }
    });
  },
  OPEN_ALL_SECTIONS(state) {
    state.sections = state.sections.map(function (_section) {
      _section.open = true;
      return _section;
    });
  },
  CLOSE_ALL_SECTIONS(state) {
    state.sections = state.sections.map(function (_section) {
      _section.open = false;
      return _section;
    });
  },
  UPDATE_SECTION_REQUEST(state, sectionId) {
    $Vue.set(state.statusUpdateSection, sectionId, 'updating');
  },
  UPDATE_SECTION_SUCCESS(state, sectionId) {
    $Vue.set(state.statusUpdateSection, sectionId, 'successful');
  },
  UPDATE_SECTION_FAILURE(state, sectionId) {
    $Vue.set(state.statusUpdateSection, sectionId, 'failed');
  },
  UPDATE_SECTION_ITEM_REQUEST(state, itemId) {
    $Vue.set(state.statusUpdateSectionItem, itemId, 'updating');
  },
  UPDATE_SECTION_ITEM_SUCCESS(state, itemId) {
    $Vue.set(state.statusUpdateSectionItem, itemId, 'successful');
  },
  UPDATE_SECTION_ITEM_FAILURE(state, itemId) {
    $Vue.set(state.statusUpdateSectionItem, itemId, 'failed');
  },
  APPEND_EMPTY_ITEM_TO_SECTION(state, data) {
    const section = state.sections.find(function (section) {
      return parseInt(section.id) === parseInt(data.section_id);
    });
    if (!section) {
      return;
    }
    section.items.push({
      id: data.item.id,
      title: data.item.title,
      type: 'empty-item'
    });
  },
  UPDATE_ITEM_SECTION_BY_ID(state, data) {
    const section = state.sections.find(function (section) {
      return parseInt(section.id) === parseInt(data.section_id);
    });
    if (!section) {
      return;
    }
    for (let i = 0; i < section.items.length; i++) {
      try {
        if (!section.items[i]) {
          continue;
        }
        const item_id = section.items[i].id;
        if (item_id) {
          if (data.items[item_id]) {
            $Vue.set(section.items, i, data.items[item_id]);
          }
        }
      } catch (ex) {
        console.log(ex);
      }
    }

    //section.items.push({id: data.item.id, title: data.item.title, type: 'empty-item'});
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseCurriculum);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/mutations/course.js":
/*!*************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/mutations/course.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Course = {
  UPDATE_HEART_BEAT: function (state, status) {
    state.heartbeat = !!status;
  },
  UPDATE_AUTO_DRAFT_STATUS: function (state, status) {
    state.auto_draft = status;
  },
  UPDATE_STATUS: function (state, status) {
    state.status = status;
  },
  INCREASE_NUMBER_REQUEST: function (state) {
    state.countCurrentRequest++;
  },
  DECREASE_NUMBER_REQUEST: function (state) {
    state.countCurrentRequest--;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Course);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/mutations/modal-course-items.js":
/*!*************************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/mutations/modal-course-items.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const Mutations = {
  TOGGLE: function (state) {
    state.open = !state.open;
  },
  SET_SECTION: function (state, sectionId) {
    state.sectionId = sectionId;
  },
  SET_LIST_ITEMS: function (state, items) {
    state.items = items;
  },
  ADD_ITEM: function (state, item) {
    state.addedItems.push(item);
  },
  REMOVE_ADDED_ITEM: function (state, item) {
    state.addedItems.forEach(function (_item, index) {
      if (_item.id === item.id) {
        state.addedItems.splice(index, 1);
      }
    });
  },
  RESET: function (state) {
    state.addedItems = [];
    state.items = [];
  },
  UPDATE_PAGINATION: function (state, pagination) {
    state.pagination = pagination;
  },
  SEARCH_ITEMS_REQUEST: function (state) {
    state.status = 'loading';
  },
  SEARCH_ITEMS_SUCCESS: function (state) {
    state.status = 'successful';
  },
  SEARCH_ITEMS_FAILURE: function (state) {
    state.status = 'failed';
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Mutations);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/store/course-section.js":
/*!*****************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/store/course-section.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _actions_course_section__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/course-section */ "./assets/src/apps/js/admin/editor/actions/course-section.js");
/* harmony import */ var _mutations_course_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../mutations/course-section */ "./assets/src/apps/js/admin/editor/mutations/course-section.js");
/* harmony import */ var _getters_course_section__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../getters/course-section */ "./assets/src/apps/js/admin/editor/getters/course-section.js");



const $ = window.jQuery;
/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(data) {
  var state = $.extend({}, data.sections);
  state.statusUpdateSection = {};
  state.statusUpdateSectionItem = {};
  state.sections = state.sections.map(function (section) {
    var hiddenSections = state.hidden_sections;
    var find = hiddenSections.find(function (sectionId) {
      return parseInt(section.id) === parseInt(sectionId);
    });
    section.open = !find;
    return section;
  });
  return {
    namespaced: true,
    state: state,
    getters: _getters_course_section__WEBPACK_IMPORTED_MODULE_2__["default"],
    mutations: _mutations_course_section__WEBPACK_IMPORTED_MODULE_1__["default"],
    actions: _actions_course_section__WEBPACK_IMPORTED_MODULE_0__["default"]
  };
}

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/store/course.js":
/*!*********************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/store/course.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _store_modal_course_items__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store/modal-course-items */ "./assets/src/apps/js/admin/editor/store/modal-course-items.js");
/* harmony import */ var _store_course_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../store/course-section */ "./assets/src/apps/js/admin/editor/store/course-section.js");
/* harmony import */ var _store_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../store/i18n */ "./assets/src/apps/js/admin/editor/store/i18n.js");
/* harmony import */ var _getters_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../getters/course */ "./assets/src/apps/js/admin/editor/getters/course.js");
/* harmony import */ var _mutations_course__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../mutations/course */ "./assets/src/apps/js/admin/editor/mutations/course.js");
/* harmony import */ var _actions_course__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/course */ "./assets/src/apps/js/admin/editor/actions/course.js");






const $ = window.jQuery;
const Course = function Course(data) {
  var state = $.extend({}, data.root);
  state.status = 'success';
  state.heartbeat = true;
  state.countCurrentRequest = 0;
  return {
    state: state,
    getters: _getters_course__WEBPACK_IMPORTED_MODULE_3__["default"],
    mutations: _mutations_course__WEBPACK_IMPORTED_MODULE_4__["default"],
    actions: _actions_course__WEBPACK_IMPORTED_MODULE_5__["default"],
    modules: {
      ci: (0,_store_modal_course_items__WEBPACK_IMPORTED_MODULE_0__["default"])(data),
      i18n: (0,_store_i18n__WEBPACK_IMPORTED_MODULE_2__["default"])(data.i18n),
      ss: (0,_store_course_section__WEBPACK_IMPORTED_MODULE_1__["default"])(data)
    }
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Course);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/store/i18n.js":
/*!*******************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/store/i18n.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
const $ = window.jQuery || jQuery;
const i18n = function i18n(i18n) {
  const state = $.extend({}, i18n);
  const getters = {
    all: function (state) {
      return state;
    }
  };
  return {
    namespaced: true,
    state: state,
    getters: getters
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18n);

/***/ }),

/***/ "./assets/src/apps/js/admin/editor/store/modal-course-items.js":
/*!*********************************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/store/modal-course-items.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _getters_modal_course_items__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../getters/modal-course-items */ "./assets/src/apps/js/admin/editor/getters/modal-course-items.js");
/* harmony import */ var _mutations_modal_course_items__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../mutations/modal-course-items */ "./assets/src/apps/js/admin/editor/mutations/modal-course-items.js");
/* harmony import */ var _actions_modal_course_items__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/modal-course-items */ "./assets/src/apps/js/admin/editor/actions/modal-course-items.js");



const $ = window.jQuery || jQuery;
/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(data) {
  var state = $.extend({}, data.chooseItems);
  state.sectionId = false;
  state.pagination = '';
  state.status = '';
  return {
    namespaced: true,
    state: state,
    getters: _getters_modal_course_items__WEBPACK_IMPORTED_MODULE_0__["default"],
    mutations: _mutations_modal_course_items__WEBPACK_IMPORTED_MODULE_1__["default"],
    actions: _actions_modal_course_items__WEBPACK_IMPORTED_MODULE_2__["default"]
  };
}

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
/*!***************************************************!*\
  !*** ./assets/src/apps/js/admin/editor/course.js ***!
  \***************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./http */ "./assets/src/apps/js/admin/editor/http.js");
/* harmony import */ var _store_course__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store/course */ "./assets/src/apps/js/admin/editor/store/course.js");


window.$Vue = window.$Vue || Vue;
window.$Vuex = window.$Vuex || Vuex;
const $ = window.jQuery;

/**
 * Init app.
 *
 * @since 3.0.0
 */
$(document).ready(function () {
  window.LP_Curriculum_Store = new $Vuex.Store((0,_store_course__WEBPACK_IMPORTED_MODULE_1__["default"])(lpAdminCourseEditorSettings));
  (0,_http__WEBPACK_IMPORTED_MODULE_0__["default"])({
    ns: 'LPCurriculumRequest',
    store: LP_Curriculum_Store
  });
  setTimeout(() => {
    window.LP_Course_Editor = new $Vue({
      el: '#admin-editor-lp_course',
      template: '<lp-course-editor></lp-course-editor>'
    });
  }, 100);
});
/******/ })()
;
//# sourceMappingURL=course.js.map