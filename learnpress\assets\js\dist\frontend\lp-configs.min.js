(()=>{"use strict";var t={d:(e,o)=>{for(var n in o)t.o(o,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:o[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{classNames:()=>n,isQuestionCorrect:()=>s,questionBlocks:()=>r,questionChecker:()=>c,questionFooterButtons:()=>u,questionTitleParts:()=>l,quizStartBlocks:()=>a});const{Hook:o}=LP,n={Quiz:{Result:["quiz-result"],Content:["quiz-content"],Questions:["quiz-questions"],Buttons:["quiz-buttons"],Attempts:["quiz-attempts"]}},i={single_choice(){},multi_choice(){},true_or_false(){}},s={fill_in_blank:()=>!0},r=function(){return LP.Hook.applyFilters("question-blocks",["title","content","answer-options","explanation","hint","buttons"])},u=function(){return LP.Hook.applyFilters("question-footer-buttons",["instant-check"])},l=function(){return LP.Hook.applyFilters("question-title-parts",["index","title","hint","edit-permalink"])},c=function(t){const e=LP.Hook.applyFilters("question-checkers",i);return t&&e[t]?e[t]:function(){return{}}},a=function(){o.applyFilters("quiz-start-blocks",{meta:!0,description:!0,custom:"Hello"})};(window.LP=window.LP||{}).config=e})();