(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),r=r=>{const s=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"course-progress"},(0,e.createElement)("span",null,"Course passing progress: 0%"),(0,e.createElement)("div",{className:"line"}),(0,e.createElement)("span",null,"Start date: 2025"))))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-progress","title":"Course Progress","category":"learnpress-course-elements","description":"Renders template Progress Course PHP templates.","textdomain":"learnpress","keywords":["progress single course","learnpress"],"icon":"chart-line","ancestor":["learnpress/single-course"],"usesContext":[],"supports":{"multiple":false,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),l=window.wp.blocks,a=window.wp.data;let o=null;var i,p,c;i=["learnpress/learnpress//single-lp_course"],p=n,c=e=>{(0,l.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,a.subscribe)((()=>{const e={...p},t=(0,a.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),i.includes(r)?(e.ancestor=null,c(e)):(e.ancestor||(e.ancestor=[]),c(e))))})),(0,l.registerBlockType)(n.name,{...n,edit:r,save:s})})();