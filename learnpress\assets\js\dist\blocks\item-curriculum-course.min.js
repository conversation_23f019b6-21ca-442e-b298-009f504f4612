(()=>{"use strict";const e=window.React,r=window.wp.primitives,t=window.ReactJSXRuntime,s=(0,t.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,t.jsx)(r.<PERSON>,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,t.jsx)(r.<PERSON>,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]}),o=window.wp.i18n,n=window.wp.blockEditor,i=window.wp.components,l=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"learnpress/item-curriculum-course","title":"Item curriculum Course","category":"learnpress-category","description":"Renders template Single Item Curriculum Course PHP templates.","textdomain":"learnpress","keywords":["item curriculum course","learnpress"],"usesContext":[],"supports":{}}');(0,window.wp.blocks.registerBlockType)(l.name,{...l,edit:r=>{const t=(0,n.useBlockProps)();return(0,e.createElement)("div",{...t},(0,e.createElement)(i.Placeholder,{icon:s,label:(0,o.__)("Item Curriculum Course","learnpress")},(0,e.createElement)("div",null,(0,o.__)("This is an editor placeholder for the Item Curriculum Course page. Content will render content of single item curriculum course. Should be not remove it","learnpress"))))},save:e=>null})})();