(()=>{"use strict";const e=(e,t={},s={})=>{"function"==typeof s.before&&s.before(),fetch(e,{method:"GET",...t}).then((e=>e.json())).then((e=>{"function"==typeof s.success&&s.success(e)})).catch((e=>{"function"==typeof s.error&&s.error(e)})).finally((()=>{"function"==typeof s.completed&&s.completed()}))},t=()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e},s=(e,t)=>{const s=new URL(e);return Object.keys(t).forEach((e=>{s.searchParams.set(e,t[e])})),s};function n(e,t){e.split(/\s+/).forEach((e=>{t(e)}))}class i{constructor(){this._events={}}on(e,t){n(e,(e=>{const s=this._events[e]||[];s.push(t),this._events[e]=s}))}off(e,t){var s=arguments.length;0!==s?n(e,(e=>{if(1===s)return void delete this._events[e];const n=this._events[e];void 0!==n&&(n.splice(n.indexOf(t),1),this._events[e]=n)})):this._events={}}trigger(e,...t){var s=this;n(e,(e=>{const n=s._events[e];void 0!==n&&n.forEach((e=>{e.apply(s,t)}))}))}}const r=e=>(e=e.filter(Boolean)).length<2?e[0]||"":1==d(e)?"["+e.join("")+"]":"(?:"+e.join("|")+")",o=e=>{if(!a(e))return e.join("");let t="",s=0;const n=()=>{s>1&&(t+="{"+s+"}")};return e.forEach(((i,r)=>{i!==e[r-1]?(n(),t+=i,s=1):s++})),n(),t},l=e=>{let t=Array.from(e);return r(t)},a=e=>new Set(e).size!==e.length,c=e=>(e+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),d=e=>e.reduce(((e,t)=>Math.max(e,u(t))),0),u=e=>Array.from(e).length,p=e=>{if(1===e.length)return[[e]];let t=[];const s=e.substring(1);return p(s).forEach((function(s){let n=s.slice(0);n[0]=e.charAt(0)+n[0],t.push(n),n=s.slice(0),n.unshift(e.charAt(0)),t.push(n)})),t},h=[[0,65535]];let g,m;const f={},v={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let e in v){let t=v[e]||"";for(let s=0;s<t.length;s++){let n=t.substring(s,s+1);f[n]=e}}const y=new RegExp(Object.keys(f).join("|")+"|[̀-ͯ·ʾʼ]","gu"),b=(e,t="NFKD")=>e.normalize(t),O=e=>Array.from(e).reduce(((e,t)=>e+_(t)),""),_=e=>(e=b(e).toLowerCase().replace(y,(e=>f[e]||"")),b(e,"NFC")),w=e=>{const t=(e=>{const t={},s=(e,s)=>{const n=t[e]||new Set,i=new RegExp("^"+l(n)+"$","iu");s.match(i)||(n.add(c(s)),t[e]=n)};for(let t of function*(e){for(const[t,s]of e)for(let e=t;e<=s;e++){let t=String.fromCharCode(e),s=O(t);s!=t.toLowerCase()&&(s.length>3||0!=s.length&&(yield{folded:s,composed:t,code_point:e}))}}(e))s(t.folded,t.folded),s(t.folded,t.composed);return t})(e),s={};let n=[];for(let e in t){let i=t[e];i&&(s[e]=l(i)),e.length>1&&n.push(c(e))}n.sort(((e,t)=>t.length-e.length));const i=r(n);return m=new RegExp("^"+i,"u"),s},S=(e,t=1)=>(t=Math.max(t,e.length-1),r(p(e).map((e=>((e,t=1)=>{let s=0;return e=e.map((e=>(g[e]&&(s+=e.length),g[e]||e))),s>=t?o(e):""})(e,t))))),A=(e,t=!0)=>{let s=e.length>1?1:0;return r(e.map((e=>{let n=[];const i=t?e.length():e.length()-1;for(let t=0;t<i;t++)n.push(S(e.substrs[t]||"",s));return o(n)})))},I=(e,t)=>{for(const s of t){if(s.start!=e.start||s.end!=e.end)continue;if(s.substrs.join("")!==e.substrs.join(""))continue;let t=e.parts;const n=e=>{for(const s of t){if(s.start===e.start&&s.substr===e.substr)return!1;if(1!=e.length&&1!=s.length){if(e.start<s.start&&e.end>s.start)return!0;if(s.start<e.start&&s.end>e.start)return!0}}return!1};if(!(s.parts.filter(n).length>0))return!0}return!1};class C{parts;substrs;start;end;constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(e){e&&(this.parts.push(e),this.substrs.push(e.substr),this.start=Math.min(e.start,this.start),this.end=Math.max(e.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(e,t){let s=new C,n=JSON.parse(JSON.stringify(this.parts)),i=n.pop();for(const e of n)s.add(e);let r=t.substr.substring(0,e-i.start),o=r.length;return s.add({start:i.start,end:i.start+o,length:o,substr:r}),s}}const L=(e,t)=>{if(e)return e[t]},x=(e,t)=>{if(e){for(var s,n=t.split(".");(s=n.shift())&&(e=e[s]););return e}},k=(e,t,s)=>{var n,i;return e?(e+="",null==t.regex||-1===(i=e.search(t.regex))?0:(n=t.string.length/e.length,0===i&&(n+=.5),n*s)):0},E=(e,t)=>{var s=e[t];if("function"==typeof s)return s;s&&!Array.isArray(s)&&(e[t]=[s])},F=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s],s)},T=(e,t)=>"number"==typeof e&&"number"==typeof t?e>t?1:e<t?-1:0:(e=O(e+"").toLowerCase())>(t=O(t+"").toLowerCase())?1:t>e?-1:0;class q{items;settings;constructor(e,t){this.items=e,this.settings=t||{diacritics:!0}}tokenize(e,t,s){if(!e||!e.length)return[];const n=[],i=e.split(/\s+/);var r;return s&&(r=new RegExp("^("+Object.keys(s).map(c).join("|")+"):(.*)$")),i.forEach((e=>{let s,i=null,o=null;r&&(s=e.match(r))&&(i=s[1],e=s[2]),e.length>0&&(o=this.settings.diacritics?(e=>{void 0===g&&(g=w(h)),e=O(e);let t="",s=[new C];for(let n=0;n<e.length;n++){let i=e.substring(n).match(m);const r=e.substring(n,n+1),o=i?i[0]:null;let l=[],a=new Set;for(const e of s){const t=e.last();if(!t||1==t.length||t.end<=n)if(o){const t=o.length;e.add({start:n,end:n+t,length:t,substr:o}),a.add("1")}else e.add({start:n,end:n+1,length:1,substr:r}),a.add("2");else if(o){let s=e.clone(n,t);const i=o.length;s.add({start:n,end:n+i,length:i,substr:o}),l.push(s)}else a.add("3")}if(l.length>0){l=l.sort(((e,t)=>e.length()-t.length()));for(let e of l)I(e,s)||s.push(e)}else if(n>0&&1==a.size&&!a.has("3")){t+=A(s,!1);let e=new C;const n=s[0];n&&e.add(n.last()),s=[e]}}return t+=A(s,!0),t})(e)||null:c(e),o&&t&&(o="\\b"+o)),n.push({string:e,regex:o?new RegExp(o,"iu"):null,field:i})})),n}getScoreFunction(e,t){var s=this.prepareSearch(e,t);return this._getScoreFunction(s)}_getScoreFunction(e){const t=e.tokens,s=t.length;if(!s)return function(){return 0};const n=e.options.fields,i=e.weights,r=n.length,o=e.getAttrFn;if(!r)return function(){return 1};const l=1===r?function(e,t){const s=n[0].field;return k(o(t,s),e,i[s]||1)}:function(e,t){var s=0;if(e.field){const n=o(t,e.field);!e.regex&&n?s+=1/r:s+=k(n,e,1)}else F(i,((n,i)=>{s+=k(o(t,i),e,n)}));return s/r};return 1===s?function(e){return l(t[0],e)}:"and"===e.options.conjunction?function(e){var n,i=0;for(let s of t){if((n=l(s,e))<=0)return 0;i+=n}return i/s}:function(e){var n=0;return F(t,(t=>{n+=l(t,e)})),n/s}}getSortFunction(e,t){var s=this.prepareSearch(e,t);return this._getSortFunction(s)}_getSortFunction(e){var t,s=[];const n=this,i=e.options,r=!e.query&&i.sort_empty?i.sort_empty:i.sort;if("function"==typeof r)return r.bind(this);const o=function(t,s){return"$score"===t?s.score:e.getAttrFn(n.items[s.id],t)};if(r)for(let t of r)(e.query||"$score"!==t.field)&&s.push(t);if(e.query){t=!0;for(let e of s)if("$score"===e.field){t=!1;break}t&&s.unshift({field:"$score",direction:"desc"})}else s=s.filter((e=>"$score"!==e.field));return s.length?function(e,t){var n,i;for(let r of s)if(i=r.field,n=("desc"===r.direction?-1:1)*T(o(i,e),o(i,t)))return n;return 0}:null}prepareSearch(e,t){const s={};var n=Object.assign({},t);if(E(n,"sort"),E(n,"sort_empty"),n.fields){E(n,"fields");const e=[];n.fields.forEach((t=>{"string"==typeof t&&(t={field:t,weight:1}),e.push(t),s[t.field]="weight"in t?t.weight:1})),n.fields=e}return{options:n,query:e.toLowerCase().trim(),tokens:this.tokenize(e,n.respect_word_boundaries,s),total:0,items:[],weights:s,getAttrFn:n.nesting?x:L}}search(e,t){var s,n,i=this;n=this.prepareSearch(e,t),t=n.options,e=n.query;const r=t.score||i._getScoreFunction(n);e.length?F(i.items,((e,i)=>{s=r(e),(!1===t.filter||s>0)&&n.items.push({score:s,id:i})})):F(i.items,((e,t)=>{n.items.push({score:1,id:t})}));const o=i._getSortFunction(n);return o&&n.items.sort(o),n.total=n.items.length,"number"==typeof t.limit&&(n.items=n.items.slice(0,t.limit)),n}}const P=e=>null==e?null:j(e),j=e=>"boolean"==typeof e?e?"1":"0":e+"",D=e=>(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),N=(e,t)=>{var s;return function(n,i){var r=this;s&&(r.loading=Math.max(r.loading-1,0),clearTimeout(s)),s=setTimeout((function(){s=null,r.loadedSearches[n]=!0,e.call(r,n,i)}),t)}},$=(e,t,s)=>{var n,i=e.trigger,r={};for(n of(e.trigger=function(){var s=arguments[0];if(-1===t.indexOf(s))return i.apply(e,arguments);r[s]=arguments},s.apply(e,[]),e.trigger=i,t))n in r&&i.apply(e,r[n])},H=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},M=(e,t,s,n)=>{e.addEventListener(t,s,n)},V=(e,t)=>!!t&&!!t[e]&&1==(t.altKey?1:0)+(t.ctrlKey?1:0)+(t.shiftKey?1:0)+(t.metaKey?1:0),B=(e,t)=>e.getAttribute("id")||(e.setAttribute("id",t),t),R=e=>e.replace(/[\\"']/g,"\\$&"),z=(e,t)=>{t&&e.append(t)},K=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s],s)},U=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Q(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},Q=e=>"string"==typeof e&&e.indexOf("<")>-1,G=(e,t)=>{var s=document.createEvent("HTMLEvents");s.initEvent(t,!0,!1),e.dispatchEvent(s)},J=(e,t)=>{Object.assign(e.style,t)},W=(e,...t)=>{var s=Y(t);(e=Z(e)).map((e=>{s.map((t=>{e.classList.add(t)}))}))},X=(e,...t)=>{var s=Y(t);(e=Z(e)).map((e=>{s.map((t=>{e.classList.remove(t)}))}))},Y=e=>{var t=[];return K(e,(e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))})),t.filter(Boolean)},Z=e=>(Array.isArray(e)||(e=[e]),e),ee=(e,t,s)=>{if(!s||s.contains(e))for(;e&&e.matches;){if(e.matches(t))return e;e=e.parentNode}},te=(e,t=0)=>t>0?e[e.length-1]:e[0],se=(e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var s=0;e=e.previousElementSibling;)e.matches(t)&&s++;return s},ne=(e,t)=>{K(t,((t,s)=>{null==t?e.removeAttribute(s):e.setAttribute(s,""+t)}))},ie=(e,t)=>{e.parentNode&&e.parentNode.replaceChild(t,e)},re="undefined"!=typeof navigator&&/Mac/.test(navigator.userAgent)?"metaKey":"ctrlKey",oe={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,refreshThrottle:300,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(e){return e.length>0},render:{}};function le(e,t){var s=Object.assign({},oe,t),n=s.dataAttr,i=s.labelField,r=s.valueField,o=s.disabledField,l=s.optgroupField,a=s.optgroupLabelField,c=s.optgroupValueField,d=e.tagName.toLowerCase(),u=e.getAttribute("placeholder")||e.getAttribute("data-placeholder");if(!u&&!s.allowEmptyOption){let t=e.querySelector('option[value=""]');t&&(u=t.textContent)}var p={placeholder:u,options:[],optgroups:[],items:[],maxItems:null};return"select"===d?(()=>{var t,d=p.options,u={},h=1;let g=0;var m=e=>{var t=Object.assign({},e.dataset),s=n&&t[n];return"string"==typeof s&&s.length&&(t=Object.assign(t,JSON.parse(s))),t},f=(e,t)=>{var n=P(e.value);if(null!=n&&(n||s.allowEmptyOption)){if(u.hasOwnProperty(n)){if(t){var a=u[n][l];a?Array.isArray(a)?a.push(t):u[n][l]=[a,t]:u[n][l]=t}}else{var c=m(e);c[i]=c[i]||e.textContent,c[r]=c[r]||n,c[o]=c[o]||e.disabled,c[l]=c[l]||t,c.$option=e,c.$order=c.$order||++g,u[n]=c,d.push(c)}e.selected&&p.items.push(n)}};p.maxItems=e.hasAttribute("multiple")?null:1,K(e.children,(e=>{var s,n,i;"optgroup"===(t=e.tagName.toLowerCase())?((i=m(s=e))[a]=i[a]||s.getAttribute("label")||"",i[c]=i[c]||h++,i[o]=i[o]||s.disabled,i.$order=i.$order||++g,p.optgroups.push(i),n=i[c],K(s.children,(e=>{f(e,n)}))):"option"===t&&f(e)}))})():(()=>{const t=e.getAttribute(n);if(t)p.options=JSON.parse(t),K(p.options,(e=>{p.items.push(e[r])}));else{var o=e.value.trim()||"";if(!s.allowEmptyOption&&!o.length)return;const t=o.split(s.delimiter);K(t,(e=>{const t={};t[i]=e,t[r]=e,p.options.push(t)})),p.items=t}})(),Object.assign({},oe,p,t)}var ae=0;class ce extends(function(e){return e.plugins={},class extends e{constructor(){super(...arguments),this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(t,s){e.plugins[t]={name:t,fn:s}}initializePlugins(e){var t,s;const n=this,i=[];if(Array.isArray(e))e.forEach((e=>{"string"==typeof e?i.push(e):(n.plugins.settings[e.name]=e.options,i.push(e.name))}));else if(e)for(t in e)e.hasOwnProperty(t)&&(n.plugins.settings[t]=e[t],i.push(t));for(;s=i.shift();)n.require(s)}loadPlugin(t){var s=this,n=s.plugins,i=e.plugins[t];if(!e.plugins.hasOwnProperty(t))throw new Error('Unable to find "'+t+'" plugin');n.requested[t]=!0,n.loaded[t]=i.fn.apply(s,[s.plugins.settings[t]||{}]),n.names.push(t)}require(e){var t=this,s=t.plugins;if(!t.plugins.loaded.hasOwnProperty(e)){if(s.requested[e])throw new Error('Plugin has circular dependency ("'+e+'")');t.loadPlugin(e)}return s.loaded[e]}}}(i)){constructor(e,t){var s;super(),this.order=0,this.isOpen=!1,this.isDisabled=!1,this.isReadOnly=!1,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],this.refreshTimeout=null,ae++;var n=U(e);if(n.tomselect)throw new Error("Tom Select already initialized on this element");n.tomselect=this,s=(window.getComputedStyle&&window.getComputedStyle(n,null)).getPropertyValue("direction");const i=le(n,t);this.settings=i,this.input=n,this.tabIndex=n.tabIndex||0,this.is_select_tag="select"===n.tagName.toLowerCase(),this.rtl=/rtl/i.test(s),this.inputId=B(n,"tomselect-"+ae),this.isRequired=n.required,this.sifter=new q(this.options,{diacritics:i.diacritics}),i.mode=i.mode||(1===i.maxItems?"single":"multi"),"boolean"!=typeof i.hideSelected&&(i.hideSelected="multi"===i.mode),"boolean"!=typeof i.hidePlaceholder&&(i.hidePlaceholder="multi"!==i.mode);var r=i.createFilter;"function"!=typeof r&&("string"==typeof r&&(r=new RegExp(r)),r instanceof RegExp?i.createFilter=e=>r.test(e):i.createFilter=e=>this.settings.duplicates||!this.options[e]),this.initializePlugins(i.plugins),this.setupCallbacks(),this.setupTemplates();const o=U("<div>"),l=U("<div>"),a=this._render("dropdown"),c=U('<div role="listbox" tabindex="-1">'),d=this.input.getAttribute("class")||"",u=i.mode;var p;W(o,i.wrapperClass,d,u),W(l,i.controlClass),z(o,l),W(a,i.dropdownClass,u),i.copyClassesToDropdown&&W(a,d),W(c,i.dropdownContentClass),z(a,c),U(i.dropdownParent||o).appendChild(a),Q(i.controlInput)?(p=U(i.controlInput),K(["autocorrect","autocapitalize","autocomplete","spellcheck"],(e=>{n.getAttribute(e)&&ne(p,{[e]:n.getAttribute(e)})})),p.tabIndex=-1,l.appendChild(p),this.focus_node=p):i.controlInput?(p=U(i.controlInput),this.focus_node=p):(p=U("<input/>"),this.focus_node=l),this.wrapper=o,this.dropdown=a,this.dropdown_content=c,this.control=l,this.control_input=p,this.setup()}setup(){const e=this,t=e.settings,s=e.control_input,n=e.dropdown,i=e.dropdown_content,r=e.wrapper,o=e.control,l=e.input,a=e.focus_node,d={passive:!0},u=e.inputId+"-ts-dropdown";ne(i,{id:u}),ne(a,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":u});const p=B(a,e.inputId+"-ts-control"),h="label[for='"+(e=>e.replace(/['"\\]/g,"\\$&"))(e.inputId)+"']",g=document.querySelector(h),m=e.focus.bind(e);if(g){M(g,"click",m),ne(g,{for:p});const t=B(g,e.inputId+"-ts-label");ne(a,{"aria-labelledby":t}),ne(i,{"aria-labelledby":t})}if(r.style.width=l.style.width,e.plugins.names.length){const t="plugin-"+e.plugins.names.join(" plugin-");W([r,n],t)}(null===t.maxItems||t.maxItems>1)&&e.is_select_tag&&ne(l,{multiple:"multiple"}),t.placeholder&&ne(s,{placeholder:t.placeholder}),!t.splitOn&&t.delimiter&&(t.splitOn=new RegExp("\\s*"+c(t.delimiter)+"+\\s*")),t.load&&t.loadThrottle&&(t.load=N(t.load,t.loadThrottle)),M(n,"mousemove",(()=>{e.ignoreHover=!1})),M(n,"mouseenter",(t=>{var s=ee(t.target,"[data-selectable]",n);s&&e.onOptionHover(t,s)}),{capture:!0}),M(n,"click",(t=>{const s=ee(t.target,"[data-selectable]");s&&(e.onOptionSelect(t,s),H(t,!0))})),M(o,"click",(t=>{var n=ee(t.target,"[data-ts-item]",o);n&&e.onItemSelect(t,n)?H(t,!0):""==s.value&&(e.onClick(),H(t,!0))})),M(a,"keydown",(t=>e.onKeyDown(t))),M(s,"keypress",(t=>e.onKeyPress(t))),M(s,"input",(t=>e.onInput(t))),M(a,"blur",(t=>e.onBlur(t))),M(a,"focus",(t=>e.onFocus(t))),M(s,"paste",(t=>e.onPaste(t)));const f=t=>{const i=t.composedPath()[0];if(!r.contains(i)&&!n.contains(i))return e.isFocused&&e.blur(),void e.inputState();i==s&&e.isOpen?t.stopPropagation():H(t,!0)},v=()=>{e.isOpen&&e.positionDropdown()};M(document,"mousedown",f),M(window,"scroll",v,d),M(window,"resize",v,d),this._destroy=()=>{document.removeEventListener("mousedown",f),window.removeEventListener("scroll",v),window.removeEventListener("resize",v),g&&g.removeEventListener("click",m)},this.revertSettings={innerHTML:l.innerHTML,tabIndex:l.tabIndex},l.tabIndex=-1,l.insertAdjacentElement("afterend",e.wrapper),e.sync(!1),t.items=[],delete t.optgroups,delete t.options,M(l,"invalid",(()=>{e.isValid&&(e.isValid=!1,e.isInvalid=!0,e.refreshState())})),e.updateOriginalInput(),e.refreshItems(),e.close(!1),e.inputState(),e.isSetup=!0,l.disabled?e.disable():l.readOnly?e.setReadOnly(!0):e.enable(),e.on("change",this.onChange),W(l,"tomselected","ts-hidden-accessible"),e.trigger("initialize"),!0===t.preload&&e.preload()}setupOptions(e=[],t=[]){this.addOptions(e),K(t,(e=>{this.registerOptionGroup(e)}))}setupTemplates(){var e=this,t=e.settings.labelField,s=e.settings.optgroupLabelField,n={optgroup:e=>{let t=document.createElement("div");return t.className="optgroup",t.appendChild(e.options),t},optgroup_header:(e,t)=>'<div class="optgroup-header">'+t(e[s])+"</div>",option:(e,s)=>"<div>"+s(e[t])+"</div>",item:(e,s)=>"<div>"+s(e[t])+"</div>",option_create:(e,t)=>'<div class="create">Add <strong>'+t(e.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};e.settings.render=Object.assign({},n,e.settings.render)}setupCallbacks(){var e,t,s={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(e in s)(t=this.settings[s[e]])&&this.on(e,t)}sync(e=!0){const t=this,s=e?le(t.input,{delimiter:t.settings.delimiter}):t.settings;t.setupOptions(s.options,s.optgroups),t.setValue(s.items||[],!0),t.lastQuery=null}onClick(){var e=this;if(e.activeItems.length>0)return e.clearActiveItems(),void e.focus();e.isFocused&&e.isOpen?e.blur():e.focus()}onMouseDown(){}onChange(){G(this.input,"input"),G(this.input,"change")}onPaste(e){var t=this;t.isInputHidden||t.isLocked?H(e):t.settings.splitOn&&setTimeout((()=>{var e=t.inputValue();if(e.match(t.settings.splitOn)){var s=e.trim().split(t.settings.splitOn);K(s,(e=>{P(e)&&(this.options[e]?t.addItem(e):t.createItem(e))}))}}),0)}onKeyPress(e){var t=this;if(!t.isLocked){var s=String.fromCharCode(e.keyCode||e.which);return t.settings.create&&"multi"===t.settings.mode&&s===t.settings.delimiter?(t.createItem(),void H(e)):void 0}H(e)}onKeyDown(e){var t=this;if(t.ignoreHover=!0,t.isLocked)9!==e.keyCode&&H(e);else{switch(e.keyCode){case 65:if(V(re,e)&&""==t.control_input.value)return H(e),void t.selectAll();break;case 27:return t.isOpen&&(H(e,!0),t.close()),void t.clearActiveItems();case 40:if(!t.isOpen&&t.hasOptions)t.open();else if(t.activeOption){let e=t.getAdjacent(t.activeOption,1);e&&t.setActiveOption(e)}return void H(e);case 38:if(t.activeOption){let e=t.getAdjacent(t.activeOption,-1);e&&t.setActiveOption(e)}return void H(e);case 13:return void(t.canSelect(t.activeOption)?(t.onOptionSelect(e,t.activeOption),H(e)):(t.settings.create&&t.createItem()||document.activeElement==t.control_input&&t.isOpen)&&H(e));case 37:return void t.advanceSelection(-1,e);case 39:return void t.advanceSelection(1,e);case 9:return void(t.settings.selectOnTab&&(t.canSelect(t.activeOption)&&(t.onOptionSelect(e,t.activeOption),H(e)),t.settings.create&&t.createItem()&&H(e)));case 8:case 46:return void t.deleteSelection(e)}t.isInputHidden&&!V(re,e)&&H(e)}}onInput(e){if(this.isLocked)return;const t=this.inputValue();var s,n;this.lastValue!==t&&(this.lastValue=t,""!=t?(this.refreshTimeout&&window.clearTimeout(this.refreshTimeout),this.refreshTimeout=(s=()=>{this.refreshTimeout=null,this._onInput()},(n=this.settings.refreshThrottle)>0?window.setTimeout(s,n):(s.call(null),null))):this._onInput())}_onInput(){const e=this.lastValue;this.settings.shouldLoad.call(this,e)&&this.load(e),this.refreshOptions(),this.trigger("type",e)}onOptionHover(e,t){this.ignoreHover||this.setActiveOption(t,!1)}onFocus(e){var t=this,s=t.isFocused;if(t.isDisabled||t.isReadOnly)return t.blur(),void H(e);t.ignoreFocus||(t.isFocused=!0,"focus"===t.settings.preload&&t.preload(),s||t.trigger("focus"),t.activeItems.length||(t.inputState(),t.refreshOptions(!!t.settings.openOnFocus)),t.refreshState())}onBlur(e){if(!1!==document.hasFocus()){var t=this;if(t.isFocused){t.isFocused=!1,t.ignoreFocus=!1;var s=()=>{t.close(),t.setActiveItem(),t.setCaret(t.items.length),t.trigger("blur")};t.settings.create&&t.settings.createOnBlur?t.createItem(null,s):s()}}}onOptionSelect(e,t){var s,n=this;t.parentElement&&t.parentElement.matches("[data-disabled]")||(t.classList.contains("create")?n.createItem(null,(()=>{n.settings.closeAfterSelect&&n.close()})):void 0!==(s=t.dataset.value)&&(n.lastQuery=null,n.addItem(s),n.settings.closeAfterSelect&&n.close(),!n.settings.hideSelected&&e.type&&/click/.test(e.type)&&n.setActiveOption(t)))}canSelect(e){return!!(this.isOpen&&e&&this.dropdown_content.contains(e))}onItemSelect(e,t){var s=this;return!s.isLocked&&"multi"===s.settings.mode&&(H(e),s.setActiveItem(t,e),!0)}canLoad(e){return!!this.settings.load&&!this.loadedSearches.hasOwnProperty(e)}load(e){const t=this;if(!t.canLoad(e))return;W(t.wrapper,t.settings.loadingClass),t.loading++;const s=t.loadCallback.bind(t);t.settings.load.call(t,e,s)}loadCallback(e,t){const s=this;s.loading=Math.max(s.loading-1,0),s.lastQuery=null,s.clearActiveOption(),s.setupOptions(e,t),s.refreshOptions(s.isFocused&&!s.isInputHidden),s.loading||X(s.wrapper,s.settings.loadingClass),s.trigger("load",e,t)}preload(){var e=this.wrapper.classList;e.contains("preloaded")||(e.add("preloaded"),this.load(""))}setTextboxValue(e=""){var t=this.control_input;t.value!==e&&(t.value=e,G(t,"update"),this.lastValue=e)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(e,t){$(this,t?[]:["change"],(()=>{this.clear(t),this.addItems(e,t)}))}setMaxItems(e){0===e&&(e=null),this.settings.maxItems=e,this.refreshState()}setActiveItem(e,t){var s,n,i,r,o,l,a=this;if("single"!==a.settings.mode){if(!e)return a.clearActiveItems(),void(a.isFocused&&a.inputState());if("click"===(s=t&&t.type.toLowerCase())&&V("shiftKey",t)&&a.activeItems.length){for(l=a.getLastActive(),(i=Array.prototype.indexOf.call(a.control.children,l))>(r=Array.prototype.indexOf.call(a.control.children,e))&&(o=i,i=r,r=o),n=i;n<=r;n++)e=a.control.children[n],-1===a.activeItems.indexOf(e)&&a.setActiveItemClass(e);H(t)}else"click"===s&&V(re,t)||"keydown"===s&&V("shiftKey",t)?e.classList.contains("active")?a.removeActiveItem(e):a.setActiveItemClass(e):(a.clearActiveItems(),a.setActiveItemClass(e));a.inputState(),a.isFocused||a.focus()}}setActiveItemClass(e){const t=this,s=t.control.querySelector(".last-active");s&&X(s,"last-active"),W(e,"active last-active"),t.trigger("item_select",e),-1==t.activeItems.indexOf(e)&&t.activeItems.push(e)}removeActiveItem(e){var t=this.activeItems.indexOf(e);this.activeItems.splice(t,1),X(e,"active")}clearActiveItems(){X(this.activeItems,"active"),this.activeItems=[]}setActiveOption(e,t=!0){e!==this.activeOption&&(this.clearActiveOption(),e&&(this.activeOption=e,ne(this.focus_node,{"aria-activedescendant":e.getAttribute("id")}),ne(e,{"aria-selected":"true"}),W(e,"active"),t&&this.scrollToOption(e)))}scrollToOption(e,t){if(!e)return;const s=this.dropdown_content,n=s.clientHeight,i=s.scrollTop||0,r=e.offsetHeight,o=e.getBoundingClientRect().top-s.getBoundingClientRect().top+i;o+r>n+i?this.scroll(o-n+r,t):o<i&&this.scroll(o,t)}scroll(e,t){const s=this.dropdown_content;t&&(s.style.scrollBehavior=t),s.scrollTop=e,s.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(X(this.activeOption,"active"),ne(this.activeOption,{"aria-selected":null})),this.activeOption=null,ne(this.focus_node,{"aria-activedescendant":null})}selectAll(){const e=this;if("single"===e.settings.mode)return;const t=e.controlChildren();t.length&&(e.inputState(),e.close(),e.activeItems=t,K(t,(t=>{e.setActiveItemClass(t)})))}inputState(){var e=this;e.control.contains(e.control_input)&&(ne(e.control_input,{placeholder:e.settings.placeholder}),e.activeItems.length>0||!e.isFocused&&e.settings.hidePlaceholder&&e.items.length>0?(e.setTextboxValue(),e.isInputHidden=!0):(e.settings.hidePlaceholder&&e.items.length>0&&ne(e.control_input,{placeholder:""}),e.isInputHidden=!1),e.wrapper.classList.toggle("input-hidden",e.isInputHidden))}inputValue(){return this.control_input.value.trim()}focus(){var e=this;e.isDisabled||e.isReadOnly||(e.ignoreFocus=!0,e.control_input.offsetWidth?e.control_input.focus():e.focus_node.focus(),setTimeout((()=>{e.ignoreFocus=!1,e.onFocus()}),0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(e){return this.sifter.getScoreFunction(e,this.getSearchOptions())}getSearchOptions(){var e=this.settings,t=e.sortField;return"string"==typeof e.sortField&&(t=[{field:e.sortField}]),{fields:e.searchField,conjunction:e.searchConjunction,sort:t,nesting:e.nesting}}search(e){var t,s,n=this,i=this.getSearchOptions();if(n.settings.score&&"function"!=typeof(s=n.settings.score.call(n,e)))throw new Error('Tom Select "score" setting must be a function that returns a function');return e!==n.lastQuery?(n.lastQuery=e,t=n.sifter.search(e,Object.assign(i,{score:s})),n.currentResults=t):t=Object.assign({},n.currentResults),n.settings.hideSelected&&(t.items=t.items.filter((e=>{let t=P(e.id);return!(t&&-1!==n.items.indexOf(t))}))),t}refreshOptions(e=!0){var t,s,n,i,r,o,l,a,c,d;const u={},p=[];var h=this,g=h.inputValue();const m=g===h.lastQuery||""==g&&null==h.lastQuery;var f=h.search(g),v=null,y=h.settings.shouldOpen||!1,b=h.dropdown_content;m&&(v=h.activeOption)&&(c=v.closest("[data-group]")),i=f.items.length,"number"==typeof h.settings.maxOptions&&(i=Math.min(i,h.settings.maxOptions)),i>0&&(y=!0);const O=(e,t)=>{let s=u[e];if(void 0!==s){let e=p[s];if(void 0!==e)return[s,e.fragment]}let n=document.createDocumentFragment();return s=p.length,p.push({fragment:n,order:t,optgroup:e}),[s,n]};for(t=0;t<i;t++){let e=f.items[t];if(!e)continue;let i=e.id,l=h.options[i];if(void 0===l)continue;let a=j(i),d=h.getOption(a,!0);for(h.settings.hideSelected||d.classList.toggle("selected",h.items.includes(a)),r=l[h.settings.optgroupField]||"",s=0,n=(o=Array.isArray(r)?r:[r])&&o.length;s<n;s++){r=o[s];let e=l.$order,t=h.optgroups[r];void 0===t?r="":e=t.$order;const[n,a]=O(r,e);s>0&&(d=d.cloneNode(!0),ne(d,{id:l.$id+"-clone-"+s,"aria-selected":null}),d.classList.add("ts-cloned"),X(d,"active"),h.activeOption&&h.activeOption.dataset.value==i&&c&&c.dataset.group===r.toString()&&(v=d)),a.appendChild(d),""!=r&&(u[r]=n)}}var _;h.settings.lockOptgroupOrder&&p.sort(((e,t)=>e.order-t.order)),l=document.createDocumentFragment(),K(p,(e=>{let t=e.fragment,s=e.optgroup;if(!t||!t.children.length)return;let n=h.optgroups[s];if(void 0!==n){let e=document.createDocumentFragment(),s=h.render("optgroup_header",n);z(e,s),z(e,t);let i=h.render("optgroup",{group:n,options:e});z(l,i)}else z(l,t)})),b.innerHTML="",z(b,l),h.settings.highlight&&(_=b.querySelectorAll("span.highlight"),Array.prototype.forEach.call(_,(function(e){var t=e.parentNode;t.replaceChild(e.firstChild,e),t.normalize()})),f.query.length&&f.tokens.length&&K(f.tokens,(e=>{((e,t)=>{if(null===t)return;if("string"==typeof t){if(!t.length)return;t=new RegExp(t,"i")}const s=e=>3===e.nodeType?(e=>{var s=e.data.match(t);if(s&&e.data.length>0){var n=document.createElement("span");n.className="highlight";var i=e.splitText(s.index);i.splitText(s[0].length);var r=i.cloneNode(!0);return n.appendChild(r),ie(i,n),1}return 0})(e):((e=>{1!==e.nodeType||!e.childNodes||/(script|style)/i.test(e.tagName)||"highlight"===e.className&&"SPAN"===e.tagName||Array.from(e.childNodes).forEach((e=>{s(e)}))})(e),0);s(e)})(b,e.regex)})));var w=e=>{let t=h.render(e,{input:g});return t&&(y=!0,b.insertBefore(t,b.firstChild)),t};if(h.loading?w("loading"):h.settings.shouldLoad.call(h,g)?0===f.items.length&&w("no_results"):w("not_loading"),(a=h.canCreate(g))&&(d=w("option_create")),h.hasOptions=f.items.length>0||a,y){if(f.items.length>0){if(v||"single"!==h.settings.mode||null==h.items[0]||(v=h.getOption(h.items[0])),!b.contains(v)){let e=0;d&&!h.settings.addPrecedence&&(e=1),v=h.selectable()[e]}}else d&&(v=d);e&&!h.isOpen&&(h.open(),h.scrollToOption(v,"auto")),h.setActiveOption(v)}else h.clearActiveOption(),e&&h.isOpen&&h.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(e,t=!1){const s=this;if(Array.isArray(e))return s.addOptions(e,t),!1;const n=P(e[s.settings.valueField]);return null!==n&&!s.options.hasOwnProperty(n)&&(e.$order=e.$order||++s.order,e.$id=s.inputId+"-opt-"+e.$order,s.options[n]=e,s.lastQuery=null,t&&(s.userOptions[n]=t,s.trigger("option_add",n,e)),n)}addOptions(e,t=!1){K(e,(e=>{this.addOption(e,t)}))}registerOption(e){return this.addOption(e)}registerOptionGroup(e){var t=P(e[this.settings.optgroupValueField]);return null!==t&&(e.$order=e.$order||++this.order,this.optgroups[t]=e,t)}addOptionGroup(e,t){var s;t[this.settings.optgroupValueField]=e,(s=this.registerOptionGroup(t))&&this.trigger("optgroup_add",s,t)}removeOptionGroup(e){this.optgroups.hasOwnProperty(e)&&(delete this.optgroups[e],this.clearCache(),this.trigger("optgroup_remove",e))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(e,t){const s=this;var n,i;const r=P(e),o=P(t[s.settings.valueField]);if(null===r)return;const l=s.options[r];if(null==l)return;if("string"!=typeof o)throw new Error("Value must be set in option data");const a=s.getOption(r),c=s.getItem(r);if(t.$order=t.$order||l.$order,delete s.options[r],s.uncacheValue(o),s.options[o]=t,a){if(s.dropdown_content.contains(a)){const e=s._render("option",t);ie(a,e),s.activeOption===a&&s.setActiveOption(e)}a.remove()}c&&(-1!==(i=s.items.indexOf(r))&&s.items.splice(i,1,o),n=s._render("item",t),c.classList.contains("active")&&W(n,"active"),ie(c,n)),s.lastQuery=null}removeOption(e,t){const s=this;e=j(e),s.uncacheValue(e),delete s.userOptions[e],delete s.options[e],s.lastQuery=null,s.trigger("option_remove",e),s.removeItem(e,t)}clearOptions(e){const t=(e||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();const s={};K(this.options,((e,n)=>{t(e,n)&&(s[n]=e)})),this.options=this.sifter.items=s,this.lastQuery=null,this.trigger("option_clear")}clearFilter(e,t){return this.items.indexOf(t)>=0}getOption(e,t=!1){const s=P(e);if(null===s)return null;const n=this.options[s];if(null!=n){if(n.$div)return n.$div;if(t)return this._render("option",n)}return null}getAdjacent(e,t,s="option"){var n;if(!e)return null;n="item"==s?this.controlChildren():this.dropdown_content.querySelectorAll("[data-selectable]");for(let s=0;s<n.length;s++)if(n[s]==e)return t>0?n[s+1]:n[s-1];return null}getItem(e){if("object"==typeof e)return e;var t=P(e);return null!==t?this.control.querySelector(`[data-value="${R(t)}"]`):null}addItems(e,t){var s=this,n=Array.isArray(e)?e:[e];const i=(n=n.filter((e=>-1===s.items.indexOf(e))))[n.length-1];n.forEach((e=>{s.isPending=e!==i,s.addItem(e,t)}))}addItem(e,t){$(this,t?[]:["change","dropdown_close"],(()=>{var s,n;const i=this,r=i.settings.mode,o=P(e);if((!o||-1===i.items.indexOf(o)||("single"===r&&i.close(),"single"!==r&&i.settings.duplicates))&&null!==o&&i.options.hasOwnProperty(o)&&("single"===r&&i.clear(t),"multi"!==r||!i.isFull())){if(s=i._render("item",i.options[o]),i.control.contains(s)&&(s=s.cloneNode(!0)),n=i.isFull(),i.items.splice(i.caretPos,0,o),i.insertAtCaret(s),i.isSetup){if(!i.isPending&&i.settings.hideSelected){let e=i.getOption(o),t=i.getAdjacent(e,1);t&&i.setActiveOption(t)}i.isPending||i.settings.closeAfterSelect||i.refreshOptions(i.isFocused&&"single"!==r),0!=i.settings.closeAfterSelect&&i.isFull()?i.close():i.isPending||i.positionDropdown(),i.trigger("item_add",o,s),i.isPending||i.updateOriginalInput({silent:t})}(!i.isPending||!n&&i.isFull())&&(i.inputState(),i.refreshState())}}))}removeItem(e=null,t){const s=this;if(!(e=s.getItem(e)))return;var n,i;const r=e.dataset.value;n=se(e),e.remove(),e.classList.contains("active")&&(i=s.activeItems.indexOf(e),s.activeItems.splice(i,1),X(e,"active")),s.items.splice(n,1),s.lastQuery=null,!s.settings.persist&&s.userOptions.hasOwnProperty(r)&&s.removeOption(r,t),n<s.caretPos&&s.setCaret(s.caretPos-1),s.updateOriginalInput({silent:t}),s.refreshState(),s.positionDropdown(),s.trigger("item_remove",r,e)}createItem(e=null,t=()=>{}){3===arguments.length&&(t=arguments[2]),"function"!=typeof t&&(t=()=>{});var s,n=this,i=n.caretPos;if(e=e||n.inputValue(),!n.canCreate(e))return t(),!1;n.lock();var r=!1,o=e=>{if(n.unlock(),!e||"object"!=typeof e)return t();var s=P(e[n.settings.valueField]);if("string"!=typeof s)return t();n.setTextboxValue(),n.addOption(e,!0),n.setCaret(i),n.addItem(s),t(e),r=!0};return s="function"==typeof n.settings.create?n.settings.create.call(this,e,o):{[n.settings.labelField]:e,[n.settings.valueField]:e},r||o(s),!0}refreshItems(){var e=this;e.lastQuery=null,e.isSetup&&e.addItems(e.items),e.updateOriginalInput(),e.refreshState()}refreshState(){const e=this;e.refreshValidityState();const t=e.isFull(),s=e.isLocked;e.wrapper.classList.toggle("rtl",e.rtl);const n=e.wrapper.classList;var i;n.toggle("focus",e.isFocused),n.toggle("disabled",e.isDisabled),n.toggle("readonly",e.isReadOnly),n.toggle("required",e.isRequired),n.toggle("invalid",!e.isValid),n.toggle("locked",s),n.toggle("full",t),n.toggle("input-active",e.isFocused&&!e.isInputHidden),n.toggle("dropdown-active",e.isOpen),n.toggle("has-options",(i=e.options,0===Object.keys(i).length)),n.toggle("has-items",e.items.length>0)}refreshValidityState(){var e=this;e.input.validity&&(e.isValid=e.input.validity.valid,e.isInvalid=!e.isValid)}isFull(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems}updateOriginalInput(e={}){const t=this;var s,n;const i=t.input.querySelector('option[value=""]');if(t.is_select_tag){const r=[],o=t.input.querySelectorAll("option:checked").length;function l(e,s,n){return e||(e=U('<option value="'+D(s)+'">'+D(n)+"</option>")),e!=i&&t.input.append(e),r.push(e),(e!=i||o>0)&&(e.selected=!0),e}t.input.querySelectorAll("option:checked").forEach((e=>{e.selected=!1})),0==t.items.length&&"single"==t.settings.mode?l(i,"",""):t.items.forEach((e=>{s=t.options[e],n=s[t.settings.labelField]||"",r.includes(s.$option)?l(t.input.querySelector(`option[value="${R(e)}"]:not(:checked)`),e,n):s.$option=l(s.$option,e,n)}))}else t.input.value=t.getValue();t.isSetup&&(e.silent||t.trigger("change",t.getValue()))}open(){var e=this;e.isLocked||e.isOpen||"multi"===e.settings.mode&&e.isFull()||(e.isOpen=!0,ne(e.focus_node,{"aria-expanded":"true"}),e.refreshState(),J(e.dropdown,{visibility:"hidden",display:"block"}),e.positionDropdown(),J(e.dropdown,{visibility:"visible",display:"block"}),e.focus(),e.trigger("dropdown_open",e.dropdown))}close(e=!0){var t=this,s=t.isOpen;e&&(t.setTextboxValue(),"single"===t.settings.mode&&t.items.length&&t.inputState()),t.isOpen=!1,ne(t.focus_node,{"aria-expanded":"false"}),J(t.dropdown,{display:"none"}),t.settings.hideSelected&&t.clearActiveOption(),t.refreshState(),s&&t.trigger("dropdown_close",t.dropdown)}positionDropdown(){if("body"===this.settings.dropdownParent){var e=this.control,t=e.getBoundingClientRect(),s=e.offsetHeight+t.top+window.scrollY,n=t.left+window.scrollX;J(this.dropdown,{width:t.width+"px",top:s+"px",left:n+"px"})}}clear(e){var t=this;if(t.items.length){var s=t.controlChildren();K(s,(e=>{t.removeItem(e,!0)})),t.inputState(),e||t.updateOriginalInput(),t.trigger("clear")}}insertAtCaret(e){const t=this,s=t.caretPos,n=t.control;n.insertBefore(e,n.children[s]||null),t.setCaret(s+1)}deleteSelection(e){var t,s,n,i,r,o=this;t=e&&8===e.keyCode?-1:1,s={start:(r=o.control_input).selectionStart||0,length:(r.selectionEnd||0)-(r.selectionStart||0)};const l=[];if(o.activeItems.length)i=te(o.activeItems,t),n=se(i),t>0&&n++,K(o.activeItems,(e=>l.push(e)));else if((o.isFocused||"single"===o.settings.mode)&&o.items.length){const e=o.controlChildren();let n;t<0&&0===s.start&&0===s.length?n=e[o.caretPos-1]:t>0&&s.start===o.inputValue().length&&(n=e[o.caretPos]),void 0!==n&&l.push(n)}if(!o.shouldDelete(l,e))return!1;for(H(e,!0),void 0!==n&&o.setCaret(n);l.length;)o.removeItem(l.pop());return o.inputState(),o.positionDropdown(),o.refreshOptions(!1),!0}shouldDelete(e,t){const s=e.map((e=>e.dataset.value));return!(!s.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete(s,t))}advanceSelection(e,t){var s,n,i=this;i.rtl&&(e*=-1),i.inputValue().length||(V(re,t)||V("shiftKey",t)?(n=(s=i.getLastActive(e))?s.classList.contains("active")?i.getAdjacent(s,e,"item"):s:e>0?i.control_input.nextElementSibling:i.control_input.previousElementSibling)&&(n.classList.contains("active")&&i.removeActiveItem(s),i.setActiveItemClass(n)):i.moveCaret(e))}moveCaret(e){}getLastActive(e){let t=this.control.querySelector(".last-active");if(t)return t;var s=this.control.querySelectorAll(".active");return s?te(s,e):void 0}setCaret(e){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.setLocked(!0)}unlock(){this.setLocked(!1)}setLocked(e=this.isReadOnly||this.isDisabled){this.isLocked=e,this.refreshState()}disable(){this.setDisabled(!0),this.close()}enable(){this.setDisabled(!1)}setDisabled(e){this.focus_node.tabIndex=e?-1:this.tabIndex,this.isDisabled=e,this.input.disabled=e,this.control_input.disabled=e,this.setLocked()}setReadOnly(e){this.isReadOnly=e,this.input.readOnly=e,this.control_input.readOnly=e,this.setLocked()}destroy(){var e=this,t=e.revertSettings;e.trigger("destroy"),e.off(),e.wrapper.remove(),e.dropdown.remove(),e.input.innerHTML=t.innerHTML,e.input.tabIndex=t.tabIndex,X(e.input,"tomselected","ts-hidden-accessible"),e._destroy(),delete e.input.tomselect}render(e,t){var s,n;const i=this;if("function"!=typeof this.settings.render[e])return null;if(!(n=i.settings.render[e].call(this,t,D)))return null;if(n=U(n),"option"===e||"option_create"===e?t[i.settings.disabledField]?ne(n,{"aria-disabled":"true"}):ne(n,{"data-selectable":""}):"optgroup"===e&&(s=t.group[i.settings.optgroupValueField],ne(n,{"data-group":s}),t.group[i.settings.disabledField]&&ne(n,{"data-disabled":""})),"option"===e||"item"===e){const s=j(t[i.settings.valueField]);ne(n,{"data-value":s}),"item"===e?(W(n,i.settings.itemClass),ne(n,{"data-ts-item":""})):(W(n,i.settings.optionClass),ne(n,{role:"option",id:t.$id}),t.$div=n,i.options[s]=t)}return n}_render(e,t){const s=this.render(e,t);if(null==s)throw"HTMLElement expected";return s}clearCache(){K(this.options,(e=>{e.$div&&(e.$div.remove(),delete e.$div)}))}uncacheValue(e){const t=this.getOption(e);t&&t.remove()}canCreate(e){return this.settings.create&&e.length>0&&this.settings.createFilter.call(this,e)}hook(e,t,s){var n=this,i=n[t];n[t]=function(){var t,r;return"after"===e&&(t=i.apply(n,arguments)),r=s.apply(n,arguments),"instead"===e?r:("before"===e&&(t=i.apply(n,arguments)),t)}}}const de=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},ue=(e,t,s,n)=>{e.addEventListener(t,s,n)},pe=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(he(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},he=e=>"string"==typeof e&&e.indexOf("<")>-1,ge=(e,t,s,n)=>{e.addEventListener(t,s,n)},me=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},fe=(e,t,s,n)=>{e.addEventListener(t,s,n)},ve=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(ye(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},ye=e=>"string"==typeof e&&e.indexOf("<")>-1;ce.define("change_listener",(function(){var e;e=()=>{this.sync()},this.input.addEventListener("change",e,undefined)})),ce.define("checkbox_options",(function(e){var t=this,s=t.onOptionSelect;t.settings.hideSelected=!1;const n=Object.assign({className:"tomselect-checkbox",checkedClassNames:void 0,uncheckedClassNames:void 0},e);var i=function(e,t){t?(e.checked=!0,n.uncheckedClassNames&&e.classList.remove(...n.uncheckedClassNames),n.checkedClassNames&&e.classList.add(...n.checkedClassNames)):(e.checked=!1,n.checkedClassNames&&e.classList.remove(...n.checkedClassNames),n.uncheckedClassNames&&e.classList.add(...n.uncheckedClassNames))},r=function(e){setTimeout((()=>{var t=e.querySelector("input."+n.className);t instanceof HTMLInputElement&&i(t,e.classList.contains("selected"))}),1)};t.hook("after","setupTemplates",(()=>{var e=t.settings.render.option;t.settings.render.option=(s,r)=>{var o=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if("string"==typeof(s=e)&&s.indexOf("<")>-1){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}var s;return document.querySelector(e)})(e.call(t,s,r)),l=document.createElement("input");n.className&&l.classList.add(n.className),l.addEventListener("click",(function(e){de(e)})),l.type="checkbox";const a=null==(c=s[t.settings.valueField])?null:(e=>"boolean"==typeof e?e?"1":"0":e+"")(c);var c;return i(l,!!(a&&t.items.indexOf(a)>-1)),o.prepend(l),o}})),t.on("item_remove",(e=>{var s=t.getOption(e);s&&(s.classList.remove("selected"),r(s))})),t.on("item_add",(e=>{var s=t.getOption(e);s&&r(s)})),t.hook("instead","onOptionSelect",((e,n)=>{if(n.classList.contains("selected"))return n.classList.remove("selected"),t.removeItem(n.dataset.value),t.refreshOptions(),void de(e,!0);s.call(t,e,n),r(n)}))})),ce.define("clear_button",(function(e){const t=this,s=Object.assign({className:"clear-button",title:"Clear All",html:e=>`<div class="${e.className}" title="${e.title}">&#10799;</div>`},e);t.on("initialize",(()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if("string"==typeof(s=e)&&s.indexOf("<")>-1){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}var s;return document.querySelector(e)})(s.html(s));e.addEventListener("click",(e=>{t.isLocked||(t.clear(),"single"===t.settings.mode&&t.settings.allowEmptyOption&&t.addItem(""),e.preventDefault(),e.stopPropagation())})),t.control.appendChild(e)}))})),ce.define("drag_drop",(function(){var e=this;if("multi"!==e.settings.mode)return;var t=e.lock,s=e.unlock;let n,i=!0;e.hook("after","setupTemplates",(()=>{var t=e.settings.render.item;e.settings.render.item=(s,r)=>{const o=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if("string"==typeof(s=e)&&s.indexOf("<")>-1){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}var s;return document.querySelector(e)})(t.call(e,s,r));var l;l=o,((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s],s)})({draggable:"true"},((e,t)=>{null==e?l.removeAttribute(t):l.setAttribute(t,""+e)}));const a=e=>{e.preventDefault(),o.classList.add("ts-drag-over"),c(o,n)},c=(e,t)=>{var s,n,i;void 0!==t&&(((e,t)=>{do{var s;if(e==(t=null==(s=t)?void 0:s.previousElementSibling))return!0}while(t&&t.previousElementSibling);return!1})(t,o)?(n=t,null==(i=(s=e).parentNode)||i.insertBefore(n,s.nextSibling)):((e,t)=>{var s;null==(s=e.parentNode)||s.insertBefore(t,e)})(e,t))};return ue(o,"mousedown",(e=>{i||((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e),e.stopPropagation()})),ue(o,"dragstart",(e=>{n=o,setTimeout((()=>{o.classList.add("ts-dragging")}),0)})),ue(o,"dragenter",a),ue(o,"dragover",a),ue(o,"dragleave",(()=>{o.classList.remove("ts-drag-over")})),ue(o,"dragend",(()=>{var t;document.querySelectorAll(".ts-drag-over").forEach((e=>e.classList.remove("ts-drag-over"))),null==(t=n)||t.classList.remove("ts-dragging"),n=void 0;var s=[];e.control.querySelectorAll("[data-value]").forEach((e=>{if(e.dataset.value){let t=e.dataset.value;t&&s.push(t)}})),e.setValue(s)})),o}})),e.hook("instead","lock",(()=>(i=!1,t.call(e)))),e.hook("instead","unlock",(()=>(i=!0,s.call(e))))})),ce.define("dropdown_header",(function(e){const t=this,s=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:e=>'<div class="'+e.headerClass+'"><div class="'+e.titleRowClass+'"><span class="'+e.labelClass+'">'+e.title+'</span><a class="'+e.closeClass+'">&times;</a></div></div>'},e);t.on("initialize",(()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if("string"==typeof(s=e)&&s.indexOf("<")>-1){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}var s;return document.querySelector(e)})(s.html(s)),n=e.querySelector("."+s.closeClass);n&&n.addEventListener("click",(e=>{((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e,!0),t.close()})),t.dropdown.insertBefore(e,t.dropdown.firstChild)}))})),ce.define("caret_position",(function(){var e=this;e.hook("instead","setCaret",(t=>{"single"!==e.settings.mode&&e.control.contains(e.control_input)?(t=Math.max(0,Math.min(e.items.length,t)))==e.caretPos||e.isPending||e.controlChildren().forEach(((s,n)=>{n<t?e.control_input.insertAdjacentElement("beforebegin",s):e.control.appendChild(s)})):t=e.items.length,e.caretPos=t})),e.hook("instead","moveCaret",(t=>{if(!e.isFocused)return;const s=e.getLastActive(t);if(s){const l=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var s=0;e=e.previousElementSibling;)e.matches(t)&&s++;return s})(s);e.setCaret(t>0?l+1:l),e.setActiveItem(),i=s,n=[],((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s])})(["last-active"],(e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(n=n.concat(e))})),o=n.filter(Boolean),(r=i,Array.isArray(r)||(r=[r]),i=r).map((e=>{o.map((t=>{e.classList.remove(t)}))}))}else e.setCaret(e.caretPos+t);var n,i,r,o}))})),ce.define("dropdown_input",(function(){const e=this;e.settings.shouldOpen=!0,e.hook("before","setup",(()=>{e.focus_node=e.control,((e,...t)=>{var s,n=(e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s])})(e,(e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))})),t.filter(Boolean)})(t);(s=e,Array.isArray(s)||(s=[s]),e=s).map((e=>{n.map((t=>{e.classList.add(t)}))}))})(e.control_input,"dropdown-input");const t=pe('<div class="dropdown-input-wrap">');t.append(e.control_input),e.dropdown.insertBefore(t,e.dropdown.firstChild);const s=pe('<input class="items-placeholder" tabindex="-1" />');s.placeholder=e.settings.placeholder||"",e.control.append(s)})),e.on("initialize",(()=>{e.control_input.addEventListener("keydown",(t=>{switch(t.keyCode){case 27:return e.isOpen&&(((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(t,!0),e.close()),void e.clearActiveItems();case 9:e.focus_node.tabIndex=-1}return e.onKeyDown.call(e,t)})),e.on("blur",(()=>{e.focus_node.tabIndex=e.isDisabled?-1:e.tabIndex})),e.on("dropdown_open",(()=>{e.control_input.focus()}));const t=e.onBlur;var s;e.hook("instead","onBlur",(s=>{if(!s||s.relatedTarget!=e.control_input)return t.call(e)})),s=()=>e.onBlur(),e.control_input.addEventListener("blur",s,undefined),e.hook("before","close",(()=>{e.isOpen&&e.focus_node.focus({preventScroll:!0})}))}))})),ce.define("input_autogrow",(function(){var e=this;e.on("initialize",(()=>{var t=document.createElement("span"),s=e.control_input;t.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",e.wrapper.appendChild(t);for(const e of["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"])t.style[e]=s.style[e];var n=()=>{t.textContent=s.value,s.style.width=t.clientWidth+"px"};n(),e.on("update item_add item_remove",n),ge(s,"input",n),ge(s,"keyup",n),ge(s,"blur",n),ge(s,"update",n)}))})),ce.define("no_backspace_delete",(function(){var e=this,t=e.deleteSelection;this.hook("instead","deleteSelection",(s=>!!e.activeItems.length&&t.call(e,s)))})),ce.define("no_active_items",(function(){this.hook("instead","setActiveItem",(()=>{})),this.hook("instead","selectAll",(()=>{}))})),ce.define("optgroup_columns",(function(){var e=this,t=e.onKeyDown;e.hook("instead","onKeyDown",(s=>{var n,i,r,o;if(!e.isOpen||37!==s.keyCode&&39!==s.keyCode)return t.call(e,s);e.ignoreHover=!0,o=(e=>{for(;e&&e.matches;){if(e.matches("[data-group]"))return e;e=e.parentNode}})(e.activeOption),n=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var s=0;e=e.previousElementSibling;)e.matches(t)&&s++;return s})(e.activeOption,"[data-selectable]"),o&&(o=37===s.keyCode?o.previousSibling:o.nextSibling)&&(i=(r=o.querySelectorAll("[data-selectable]"))[Math.min(r.length-1,n)])&&e.setActiveOption(i)}))})),ce.define("remove_button",(function(e){const t=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},e);var s=this;if(t.append){var n='<a href="javascript:void(0)" class="'+t.className+'" tabindex="-1" title="'+(t.title+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")+'">'+t.label+"</a>";s.hook("after","setupTemplates",(()=>{var e=s.settings.render.item;s.settings.render.item=(t,i)=>{var r=ve(e.call(s,t,i)),o=ve(n);return r.appendChild(o),fe(o,"mousedown",(e=>{me(e,!0)})),fe(o,"click",(e=>{s.isLocked||(me(e,!0),s.isLocked||s.shouldDelete([r],e)&&(s.removeItem(r),s.refreshOptions(!1),s.inputState()))})),r}}))}})),ce.define("restore_on_backspace",(function(e){const t=this,s=Object.assign({text:e=>e[t.settings.labelField]},e);t.on("item_remove",(function(e){if(t.isFocused&&""===t.control_input.value.trim()){var n=t.options[e];n&&t.setTextboxValue(s.text.call(t,n))}}))})),ce.define("virtual_scroll",(function(){const e=this,t=e.canLoad,s=e.clearActiveOption,n=e.loadCallback;var i,r,o={},l=!1,a=[];if(e.settings.shouldLoadMore||(e.settings.shouldLoadMore=()=>{if(i.clientHeight/(i.scrollHeight-i.scrollTop)>.9)return!0;if(e.activeOption){var t=e.selectable();if(Array.from(t).indexOf(e.activeOption)>=t.length-2)return!0}return!1}),!e.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";e.settings.sortField=[{field:"$order"},{field:"$score"}];const c=t=>!("number"==typeof e.settings.maxOptions&&i.children.length>=e.settings.maxOptions||!(t in o)||!o[t]),d=(t,s)=>e.items.indexOf(s)>=0||a.indexOf(s)>=0;e.setNextUrl=(e,t)=>{o[e]=t},e.getUrl=t=>{if(t in o){const e=o[t];return o[t]=!1,e}return e.clearPagination(),e.settings.firstUrl.call(e,t)},e.clearPagination=()=>{o={}},e.hook("instead","clearActiveOption",(()=>{if(!l)return s.call(e)})),e.hook("instead","canLoad",(s=>s in o?c(s):t.call(e,s))),e.hook("instead","loadCallback",((t,s)=>{if(l){if(r){const s=t[0];void 0!==s&&(r.dataset.value=s[e.settings.valueField])}}else e.clearOptions(d);n.call(e,t,s),l=!1})),e.hook("after","refreshOptions",(()=>{const t=e.lastValue;var s;c(t)?(s=e.render("loading_more",{query:t}))&&(s.setAttribute("data-selectable",""),r=s):t in o&&!i.querySelector(".no-results")&&(s=e.render("no_more_results",{query:t})),s&&(((e,...t)=>{var s,n=(e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var s in e)e.hasOwnProperty(s)&&t(e[s])})(e,(e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))})),t.filter(Boolean)})(t);(s=e,Array.isArray(s)||(s=[s]),e=s).map((e=>{n.map((t=>{e.classList.add(t)}))}))})(s,e.settings.optionClass),i.append(s))})),e.on("initialize",(()=>{a=Object.keys(e.options),i=e.dropdown_content,e.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},e.settings.render),i.addEventListener("scroll",(()=>{e.settings.shouldLoadMore.call(e)&&c(e.lastValue)&&(l||(l=!0,e.load.call(e,e.lastValue)))}))}))}));const be=ce,Oe={};let _e;"undefined"!=typeof lpDataAdmin&&(_e=lpDataAdmin.lp_rest_url,Oe.admin={apiAdminNotice:_e+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:_e+"lp/v1/orders/statistic",apiAddons:_e+"lp/v1/addon/all",apiAddonAction:_e+"lp/v1/addon/action-n",apiAddonsPurchase:_e+"lp/v1/addon/info-addons-purchase",apiSearchCourses:_e+"lp/v1/admin/tools/search-course",apiSearchUsers:_e+"lp/v1/admin/tools/search-user",apiAssignUserCourse:_e+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:_e+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(_e=lpData.lp_rest_url,Oe.frontend={apiWidgets:_e+"lp/v1/widgets/api",apiCourses:_e+"lp/v1/courses/archive-course",apiAJAX:_e+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:_e+"lp/v1/profile/cover-image"}),_e&&(Oe.apiCourses=_e+"lp/v1/courses/");const we=Oe,Se={buildTomSelect(e,t,s,n,i){if(!e)return;const r={plugins:{remove_button:{title:"Remove this item"}},onInitialize(){},onItemAdd(t){if(s){const r=Array.from(e.selectedOptions).map((e=>e.value));r.push(t),n.id_not_in=r.join(","),s("",n,i)}}};s&&(r.load=(t,r)=>{const o=Array.from(e.selectedOptions).map((e=>e.value));n.id_not_in=o.join(","),s(t,n,Se.callBackTomSelectSearchAPI(r,i))});const o=(t={...r,...t}).options;if(t?.options?.length>20){const s=20,n=t.options.length;let i=0;const r={...t};r.options=o.slice(i,s);const l=new be(e,r);i+=s;const a=setInterval((()=>{i>n-1&&clearInterval(a);const e=o.slice(i,i+s);i+=s,l.addOptions(e),l.setValue(t.items)}),200);return l}return new be(e,t)},callBackTomSelectSearchAPI:(e,t)=>({success:s=>{const n=t.success(s);e(n)}}),fetchCourses(t="",s={},n){const i=we.admin.apiSearchCourses;s.search=t;const r={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(s)};e(i,r,n)},fetchUsers(t="",s={},n){const i=we.admin.apiSearchUsers;s.search=t;const r={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(s)};e(i,r,n)}};!function(){let e,t;document.addEventListener("click",(t=>{const s=t.target;if("lp-invoice__export"===s.id)e.save();else if("lp-invoice__update"===s.id){const e=document.querySelector(".export-options__content").querySelectorAll("input"),t=[];e.forEach((e=>{e.checked||t.push(e.name)})),window.localStorage.setItem("lp_invoice_un_fields",JSON.stringify(t)),window.localStorage.setItem("lp_invoice_show",1),window.location.reload()}})),document.addEventListener("DOMContentLoaded",(()=>{if(!document.querySelector("#order-export__section").length){document.querySelectorAll(".tabs");const s=document.querySelectorAll(".tab"),n=document.querySelectorAll(".panel");function i(e){for(let e=0;e<s.length;e++)s[e].classList.remove("active");for(let e=0;e<n.length;e++)n[e].classList.remove("active");e.target.classList.add("active");const t=e.target.getAttribute("data-target");document.getElementById("panels").getElementsByClassName(t)[0].classList.add("active")}for(let l=0;l<s.length;l++)s[l].addEventListener("click",i,!1);t=document.getElementById("myModal");const r=document.getElementById("order-export__button"),o=document.getElementsByClassName("close")[0];r.onclick=function(){t.style.display="block"},o.onclick=function(){t.style.display="none",window.localStorage.setItem("lp_invoice_show",0)},window.onclick=function(e){e.target===t&&(t.style.display="none",window.localStorage.setItem("lp_invoice_show",0))},(()=>{const e=window.localStorage.getItem("lp_invoice_un_fields"),s=document.querySelector(".export-options__content");document.querySelectorAll(".invoice-field").forEach((t=>{const n=t.classList[1];if(e&&e.includes(n)){t.remove();const e=s.querySelector(`[name=${n}]`);e&&(e.checked=!1)}})),1===parseInt(window.localStorage.getItem("lp_invoice_show"))&&(t.style.display="block")})(),(()=>{const t={margin:[0,0,0,5],filename:document.title,image:{type:"webp"},html2canvas:{scale:2.5},jsPDF:{format:"a4",orientation:"p"}},s=document.querySelector("#lp-invoice__content");e=html2pdf().set(t).from(s)})()}}))}(),(()=>{let n,i,r,o,l,a,c,d,u,p;const h="#modal-search-items";let g={search:"",id_not_in:"",paged:1};const m=[],f=[],v=(e="",t=[],s=1)=>{let i="";t.length>0&&(i=t.join(",")),g={search:e,id_not_in:i,paged:s},Se.fetchCourses(e,g,{before(){n.classList.add("loading")},success(e){const{data:t,status:i,message:o}=e,{courses:l,total_pages:a}=t;if("success"!==i)console.error(o);else{if(!l.length)return void(r.innerHTML='<li class="lp-result-item">No courses found</li>');r.innerHTML=y(l);const e=b(s,a);n.querySelector(".search-nav").innerHTML=e}},error(e){console.error(e)},completed(){n.classList.remove("loading")}})},y=e=>{let t="";return e.forEach((e=>{const s=parseInt(e.ID),n=m.includes(s)?"checked":"";t+=`\n\t\t\t<li class="lp-result-item" data-id="${s}" data-type="lp_course" data-text="${e.post_title}">\n\t\t\t\t<label>\n\t\t\t\t\t<input type="checkbox" value="${s}" name="selectedItems[]" ${n}>\n\t\t\t\t\t<span class="lp-item-text">${e.post_title} (#${s})</span>\n\t\t\t\t</label>\n\t\t\t</li>`})),t},b=(e,t)=>{e=parseInt(e);let s="";if((t=parseInt(t))<=1)return s;const n=e+1,i=e-1;let r=[];if(t<=9)for(let e=1;e<=t;e++)r.push(e);else if(e<=3)r=[1,2,3,4,5,"x",t];else if(e<=5){for(let t=1;t<=e;t++)r.push(t);for(let t=1;t<=2;t++){const s=e+t;r.push(s)}r.push("x"),r.push(t)}else{r=[1,"x"];for(let t=2;t>=0;t--){const s=e-t;r.push(s)}if(t-e<=5)for(let s=e+1;s<=t;s++)r.push(s);else{for(let t=1;t<=2;t++){const s=e+t;r.push(s)}r.push("x"),r.push(t)}}const o=r.length;1!==e&&(s+=`<a class="prev page-numbers button" href="#" data-page="${i}"><</a>`);for(let t=0;t<o;t++)e===parseInt(r[t])?s+=`<a aria-current="page" class="page-numbers current button disabled" data-page="${r[t]}">\n\t\t\t\t${r[t]}\n\t\t\t</a>`:"x"===r[t]?s+='<span class="page-numbers dots button disabled">...</span>':s+=`<a class="page-numbers button" href="#" data-page="${r[t]}">${r[t]} </a>`;return e!==t&&(s+=`<a class="next page-numbers button" href="#" data-page="${n}">></a>`),s};document.addEventListener("click",(l=>{const c=l.target;if(i&&c.id===i.id&&(l.preventDefault(),a.style.display="block",d.style.display="none",r.innerHTML="",v(g.search,f,g.paged)),c.classList.contains("close")&&c.closest(h)&&(l.preventDefault(),n.querySelector('input[name="search"]').value="",g.search="",g.paged=1,a.style.display="none"),c.classList.contains("page-numbers")&&c.closest(h)){l.preventDefault();const e=c.getAttribute("data-page");v(g.search,g.id_not_in,e)}if("selectedItems[]"===c.name&&c.closest(h)){const e=parseInt(c.value);if(c.checked)m.push(e);else{const t=m.indexOf(e);t>-1&&m.splice(t,1)}d.style.display=m.length>0?"block":"none"}((n,i)=>{if(!i.classList.contains("add"))return;if(!i.closest(h))return;n.preventDefault(),i.disabled=!0;const r={"lp-ajax":"add_items_to_order",order_id:document.querySelector("#post_ID").value,items:m,nonce:lpDataAdmin.nonce},l={success(e){const{data:t,messages:s,status:n}=e;if("error"===n)return void console.error(s);const{item_html:i,order_data:r}=t,l=u.querySelector(".no-order-items");l.style.display="none",l.insertAdjacentHTML("beforebegin",i),o.querySelector(".order-subtotal").innerHTML=r.subtotal_html,o.querySelector(".order-total").innerHTML=r.total_html,f.push(...m),m.splice(0,m.length)},error(e){console.error(e)},completed(){i.disabled=!1,a.style.display="none"}};e(s(t(),r),{},l)})(l,c),((n,i)=>{if("SPAN"!==i.tagName)return;if(!i.closest("#learn-press-order"))return;if(n.preventDefault(),!confirm("Are you sure you want to remove this item?"))return;i.disabled=!0,i.classList.add("dashicons-update");const r=i.closest(".order-item-row"),l=i.closest(".list-order-items"),a=parseInt(r.getAttribute("data-item_id")),c=parseInt(r.getAttribute("data-id")),d={"lp-ajax":"remove_items_from_order",order_id:document.querySelector("#post_ID").value,items:a,nonce:lpDataAdmin.nonce},u={success(e){const{data:t,messages:s,status:n}=e;if("error"===n)return void console.error(s);const{item_html:i,order_data:r}=t,a=l.querySelector(".no-order-items");l.querySelectorAll(".order-item-row").forEach((e=>{e.remove()})),i.length?a.insertAdjacentHTML("beforebegin",i):a.style.display="block",m.splice(m.indexOf(c),1),f.splice(m.indexOf(c),1),o.querySelector(".order-subtotal").innerHTML=r.subtotal_html,o.querySelector(".order-total").innerHTML=r.total_html},error(e){console.error(e)},completed(){}};e(s(t(),d),{},u)})(l,c)})),document.addEventListener("keyup",(function(e){((e,t)=>{if("search"!==t.name)return;if(!t.closest(h))return;e.preventDefault();const s=t.value;(!s||s&&s.length>2)&&(void 0!==p&&clearTimeout(p),p=setTimeout((function(){v(s,f,1)}),800))})(e,e.target)})),document.addEventListener("DOMContentLoaded",(()=>{o=document.querySelector("#learn-press-order"),u=o.querySelector(".list-order-items"),i=o.querySelector("#learn-press-add-order-item"),l=document.querySelector("#learn-press-modal-search-items"),a=document.querySelector("#container-modal-search-items"),o&&i&&(document.querySelectorAll("#learn-press-order .list-order-items tbody .order-item-row").forEach((e=>{const t=parseInt(e.getAttribute("data-id"));f.push(t)})),a.innerHTML=l.innerHTML,n=a.querySelector(h),r=n.querySelector(".search-results"),c=n.querySelector("footer"),d=c.querySelector(".add"),a.style.display="none")}))})()})();