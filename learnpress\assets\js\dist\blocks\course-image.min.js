(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),r=r=>{const{attributes:s,setAttributes:n,context:l}=r,o=(0,t.useBlockProps)(),{lpCourseData:a}=l,i=a?.image||'<div className="course-img"><img src="https://placehold.co/500x300?text=Course+Image"/></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("a",null,(0,e.createElement)("div",{...o,dangerouslySetInnerHTML:{__html:i}})))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-image","title":"Course Image","category":"learnpress-course-elements","icon":"format-image","description":"Renders template Image Course PHP templates.","textdomain":"learnpress","keywords":["image single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"html":false,"shadow":true,"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}}}}'),l=window.wp.blocks,o=window.wp.data;let a=null;var i,c,u;i=["learnpress/learnpress//single-lp_course"],c=n,u=e=>{(0,l.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,o.subscribe)((()=>{const e={...c},t=(0,o.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&a!==r&&(a=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),i.includes(r)?(e.ancestor=null,u(e)):(e.ancestor||(e.ancestor=[]),u(e))))})),(0,l.registerBlockType)(n.name,{...n,edit:r,save:s})})();