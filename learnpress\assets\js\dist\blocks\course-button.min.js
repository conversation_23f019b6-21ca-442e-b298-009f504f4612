(()=>{"use strict";const e=window.React,t=window.wp.i18n,r=window.wp.blockEditor,s=s=>{const n=(0,r.useBlockProps)(),{attributes:o,setAttributes:l,context:a}=s,{lpCourseData:u}=a;let i=n.className;return i=i.replaceAll("wp-block-learnpress-course-button",""),(0,e.createElement)("div",{className:i},(0,e.createElement)("button",{...n},(0,t.__)("Buy Now","learnpress")))},n=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-button","title":"Course Button","category":"learnpress-course-elements","icon":"button","description":"Renders template Button Course PHP templates.","textdomain":"learnpress","keywords":["button single course","learnpress"],"ancestor":["learnpress/single-course"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":true,"text":true,"__experimentalDefaultControls":{"background":true,"text":true}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"width":false,"color":false,"radius":false}},"spacing":{"margin":true,"padding":true,"content":true,"__experimentalDefaultControls":{"margin":false,"padding":false,"content":true}}}}'),l=window.wp.blocks,a=window.wp.data;let u=null;var i,c,p;i=["learnpress/learnpress//single-lp_course"],c=o,p=e=>{(0,l.registerBlockType)(e.name,{...e,edit:s,save:n})},(0,a.subscribe)((()=>{const e={...c},t=(0,a.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&u!==r&&(u=r,(0,l.getBlockType)(e.name)&&((0,l.unregisterBlockType)(e.name),i.includes(r)?(e.ancestor=null,p(e)):(e.ancestor||(e.ancestor=[]),p(e))))})),(0,l.registerBlockType)(o.name,{...o,edit:s,save:n})})();