(()=>{"use strict";const e={};let s;"undefined"!=typeof lpDataAdmin&&(s=lpDataAdmin.lp_rest_url,e.admin={apiAdminNotice:s+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:s+"lp/v1/orders/statistic",apiAddons:s+"lp/v1/addon/all",apiAddonAction:s+"lp/v1/addon/action-n",apiAddonsPurchase:s+"lp/v1/addon/info-addons-purchase",apiSearchCourses:s+"lp/v1/admin/tools/search-course",apiSearchUsers:s+"lp/v1/admin/tools/search-user",apiAssignUserCourse:s+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:s+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(s=lpData.lp_rest_url,e.frontend={apiWidgets:s+"lp/v1/widgets/api",apiCourses:s+"lp/v1/courses/archive-course",apiAJAX:s+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:s+"lp/v1/profile/cover-image"}),s&&(e.apiCourses=s+"lp/v1/courses/");const t=()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e},o=(e,s)=>{const t=new URL(e);return Object.keys(s).forEach((e=>{t.searchParams.set(e,s[e])})),t},n=(e,s)=>{new IntersectionObserver((function(e){for(const t of e)t.isIntersecting&&s(t)})).observe(e)};let r;"undefined"==typeof lpData&&console.log("lpData is undefined"),document.addEventListener("change",(function(e){const s=e.target;window.lpCoursesList.onChangeSortBy(e,s),window.lpCoursesList.onChangeTypeLayout(e,s)})),document.addEventListener("click",(function(e){const s=e.target;window.lpCoursesList.LoadMore(e,s)})),document.addEventListener("keyup",(function(e){const s=e.target;window.lpCoursesList.searchCourse(e,s)})),document.addEventListener("submit",(function(e){e.target})),((e,s)=>{const t=document.querySelector(e);if(t)return void s(t);const o=new MutationObserver(((t,o)=>{const n=document.querySelector(e);n&&(o.disconnect(),s(n))}));o.observe(document.documentElement,{childList:!0,subtree:!0})})(".course-filter-btn-mobile",(function(e){document.querySelector(".widget_course_filter")||e.remove()})),window.lpCoursesList=(()=>{const e=".lp-list-courses-no-css",s=".lp-target",a=".courses-page-result",i=".lp-loading-no-css",c=t();return{LoadMore:(t,o)=>{const n=o.closest(".courses-btn-load-more-no-css:not(.disabled)");if(!n)return;const r=n.closest(`${s}`);if(!r)return;t.preventDefault(),n.classList.add("disabled");const c=n.querySelector(i),d={...JSON.parse(r.dataset.send)};d.args.hasOwnProperty("paged")||(d.args.paged=1),d.args.paged++,r.dataset.send=JSON.stringify(d),c&&c.classList.remove("hide");const l={success:s=>{const{status:t,message:o,data:i}=s,c=parseInt(i.paged),d=parseInt(i.total_pages),l=document.createElement("div");l.innerHTML=i.content||"";const u=r.querySelector(e),p=r.querySelector(a),g=l.querySelector(a);u.insertAdjacentHTML("beforeend",l.querySelector(e).innerHTML),p&&g&&(p.innerHTML=g.innerHTML),c>=d&&n.remove()},error:e=>{console.log(e)},completed:()=>{c&&c.classList.add("hide"),n.classList.remove("disabled")}};window.lpAJAXG.fetchAJAX(d,l)},LoadInfinite:()=>{const t=t=>{const o=t.target,n=o.querySelector(`${i}:not(.disabled)`);if(!n)return;n.classList.remove("hide"),n.classList.add("disabled");const r=o.closest(s);if(!r)return;const c={...JSON.parse(r.dataset.send)};c.args.hasOwnProperty("paged")||(c.args.paged=1),c.args.paged++,r.dataset.send=JSON.stringify(c);const d={success:s=>{const{status:t,message:n,data:i}=s,c=document.createElement("div");c.innerHTML=i.content||"";const d=r.querySelector(e),l=r.querySelector(a),u=c.querySelector(a);d.insertAdjacentHTML("beforeend",c.querySelector(e).innerHTML),l&&u&&(l.innerHTML=u.innerHTML),i.total_pages===i.paged&&o.remove()},error:e=>{console.log(e)},completed:()=>{n.classList.add("hide"),n.classList.remove("disabled")}};window.lpAJAXG.fetchAJAX(c,d)};var o;o=e=>{if(e.classList.contains("courses-load-infinite-no-css"))n(e,t);else if(e.classList.contains("wp-block-learnpress-list-courses")){const s=e.querySelector(".courses-load-infinite-no-css");s&&n(s,t)}},new MutationObserver((function(e){e.forEach((function(e){e.addedNodes&&e.addedNodes.forEach((function(e){1===e.nodeType&&o(e)}))}))})).observe(document,{childList:!0,subtree:!0});const r=document.querySelector(".courses-load-infinite-no-css");r&&n(r,t)},onChangeSortBy:(e,t)=>{if(!t.classList.contains("courses-order-by"))return;const n=t.closest(s);if(!n)return lpData.urlParams.paged=1,lpData.urlParams.order_by=t.value||"",void(window.location.href=o(c,lpData.urlParams));e.preventDefault();const r={...JSON.parse(n.dataset.send)};r.args.paged=1,r.args.order_by=t.value||"",n.dataset.send=JSON.stringify(r),lpData.urlParams.paged=r.args.paged,lpData.urlParams.order_by=r.args.order_by,window.history.pushState({},"",o(c,lpData.urlParams)),window.lpAJAXG.showHideLoading(n,0);const a={success:e=>{const{status:s,message:t,data:o}=e;n.innerHTML=o.content||""},error:e=>{console.log(e)},completed:()=>{window.lpAJAXG.showHideLoading(n,1)}};window.lpAJAXG.fetchAJAX(r,a)},onChangeTypeLayout:(s,t)=>{if("lp-switch-layout-btn"!==t.getAttribute("name"))return;const o=document.querySelector(e);if(!o)return;s.preventDefault();const n=t.value;n&&(o.dataset.layout=n,window.wpCookies.set("courses-layout",n,86400,"/"))},searchCourse:(e,n)=>{if("c_search"!==n.name)return;const a=n.closest(s);if(!a)return;e.preventDefault();const i={...JSON.parse(a.dataset.send)},c=n.value;i.args.c_search=c||"",i.args.paged=1,a.dataset.send=JSON.stringify(i),lpData.urlParams.paged=i.args.paged,lpData.urlParams.c_search=i.args.c_search,window.history.pushState({},"",o(t(),lpData.urlParams)),(!c||c&&c.length>2)&&(void 0!==r&&clearTimeout(r),r=setTimeout((function(){const e={success:e=>{const{status:s,message:t,data:o}=e;a.innerHTML=o.content||""},error:e=>{console.log(e)},completed:()=>{}};window.lpAJAXG.fetchAJAX(i,e)}),800))}}})(),window.lpCoursesList.LoadInfinite()})();