(()=>{"use strict";const e={};let t;"undefined"!=typeof lpDataAdmin&&(t=lpDataAdmin.lp_rest_url,e.admin={apiAdminNotice:t+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:t+"lp/v1/orders/statistic",apiAddons:t+"lp/v1/addon/all",apiAddonAction:t+"lp/v1/addon/action-n",apiAddonsPurchase:t+"lp/v1/addon/info-addons-purchase",apiSearchCourses:t+"lp/v1/admin/tools/search-course",apiSearchUsers:t+"lp/v1/admin/tools/search-user",apiAssignUserCourse:t+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:t+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(t=lpData.lp_rest_url,e.frontend={apiWidgets:t+"lp/v1/widgets/api",apiCourses:t+"lp/v1/courses/archive-course",apiAJAX:t+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:t+"lp/v1/profile/cover-image"}),t&&(e.apiCourses=t+"lp/v1/courses/");const s=e,o=(e,t)=>{const s=new URL(e);return Object.keys(t).forEach((e=>{s.searchParams.set(e,t[e])})),s},r={get:(e,t,s)=>{let o;if(s)o=wpCookies.get(e);else{let t=wpCookies.get("LP");t&&(t=JSON.parse(t),o=e?t[e]:t)}return o||o===t||(o=t),o},set(e,t,s,o,r,n){if(arguments.length>2)wpCookies.set(e,t,s,o,r,n);else if(2==arguments.length){let s=wpCookies.get("LP");s=s?JSON.parse(s):{},s[e]=t,wpCookies.set("LP",JSON.stringify(s),"","/")}else wpCookies.set("LP",JSON.stringify(e),"","/")},remove(e){const t=r.get(),s=new RegExp(e,"g"),o={},n=e.match(/\*/);for(const r in t)n?r.match(s)||(o[r]=t[r]):e!=r&&(o[r]=t[r]);r.set(o)}},n=r;"undefined"!=typeof lpData&&"undefined"!=typeof lpSettingCourses||console.log("lpData || lpSettingCourses is undefined"),window.lpArchiveRequestCourse=e=>{window.lpCourseList.updateEventTypeBeforeFetch("filter"),window.lpCourseList.triggerFetchAPI(e)},document.addEventListener("change",(function(e){const t=e.target;window.lpCourseList.checkIsNewListCourses()||(window.lpCourseList.onChangeSortBy(e,t),window.lpCourseList.onChangeTypeLayout(e,t))})),document.addEventListener("click",(function(e){const t=e.target;window.lpCourseList.checkIsNewListCourses()||(window.lpCourseList.clickLoadMore(e,t),window.lpCourseList.clickNumberPage(e,t))})),document.addEventListener("scroll",(function(e){const t=e.target;window.lpCourseList.checkIsNewListCourses()||window.lpCourseList.scrollInfinite(e,t)})),document.addEventListener("keyup",(function(e){const t=e.target;window.lpCourseList.checkIsNewListCourses()||window.lpCourseList.searchCourse(e,t)})),document.addEventListener("submit",(function(e){const t=e.target;window.lpCourseList.checkIsNewListCourses()||window.lpCourseList.searchCourse(e,t)})),window.lpCourseList=(()=>{const e="lp-archive-courses",t="learn-press-courses",r="lp-archive-course-skeleton",i="courses-page-result",a=parseInt(lpSettingCourses.lpArchiveLoadAjax||0),c=1===parseInt(lpSettingCourses.lpArchiveNoLoadAjaxFirst),l=lpData.urlParams||[],u=(()=>{let e=window.location.href;return e.includes("?")&&(e=e.split("?")[0]),e})();let d={};const p=lpSettingCourses.lpArchivePaginationType||"number";let f,g,w=!1;const m=(e,t={})=>{const r=o(s.frontend.apiCourses,e);let n={};0!==parseInt(lpData.user_id)&&(n={headers:{"X-WP-Nonce":lpData.nonce}}),((e,t={},s={})=>{"function"==typeof s.before&&s.before(),fetch(e,{method:"GET",...t}).then((e=>e.json())).then((e=>{"function"==typeof s.success&&s.success(e)})).catch((e=>{"function"==typeof s.error&&s.error(e)})).finally((()=>{"function"==typeof s.completed&&s.completed()}))})(r,n,t)};return{init:()=>{const e={},t=window.location.search,s=new URLSearchParams(t);for(const[t,o]of s.entries())e[t]=o;d={...l,...e},d.paged=parseInt(d.paged||1),isNaN(d.paged)&&(d.paged=1),c&&"number"!==p&&(d.paged=1),window.localStorage.setItem("lp_filter_courses",JSON.stringify(d))},updateEventTypeBeforeFetch:e=>{f=e},onChangeSortBy:(e,t)=>{if(!t.classList.contains("courses-order-by"))return;e.preventDefault();const s=JSON.parse(window.localStorage.getItem("lp_filter_courses"))||{};s.order_by=t.value||"","undefined"!=typeof lpSettingCourses&&lpData.is_course_archive&&lpSettingCourses.lpArchiveLoadAjax?window.lpCourseList.triggerFetchAPI(s):window.location.href=o(u,s)},onChangeTypeLayout:(s,o)=>{if("lp-switch-layout-btn"!==o.getAttribute("name"))return;const r=o.closest(`.${e}`);if(!r)return;const i=r.querySelector(`.${t}`);if(!i)return;s.preventDefault();const a=o.value;a&&(i.dataset.layout=a,n.set("courses-layout",a))},clickNumberPage:(t,s)=>{if(!a||parseInt(lpSettingCourses.noLoadCoursesJs))return;if(s.classList.contains("page-numbers")){if(!s.closest(`.${e}`))return;t.preventDefault();const o=d.paged;return s.classList.contains("prev")?d.paged=o-1:s.classList.contains("next")?d.paged=o+1:d.paged=parseInt(s.textContent),f="number",void window.lpCourseList.triggerFetchAPI(d)}const o=s.closest(".page-numbers");o&&(t.preventDefault(),o.click())},clickLoadMore:(s,o)=>{if(!o.classList.contains("courses-btn-load-more"))return;const r=o.closest(`.${e}`);r&&r.querySelector(`.${t}`)&&(s.preventDefault(),++d.paged,f="load-more",window.lpCourseList.triggerFetchAPI(d))},scrollInfinite:(t,s)=>{const o=document.querySelector(`.${e}`);if(!o)return;const r=o.querySelector(".courses-load-infinite");r&&new IntersectionObserver((function(e){for(const t of e)if(t.isIntersecting){if(w)return;++d.paged,f="infinite",window.lpCourseList.triggerFetchAPI(d)}})).observe(r)},triggerFetchAPI:s=>{const o=document.querySelector(`.${e}`);if(!o)return;const r=o.querySelector(`.${t}`);if(!r)return;let n;switch(d=s,f){case"load-more":n=window.lpCourseList.callBackPaginationTypeLoadMore(o,r);break;case"infinite":n=window.lpCourseList.callBackPaginationTypeInfinite(o,r);break;case"custom":n=s.customCallBack||!1;break;default:n=window.lpCourseList.callBackFilter(s,o,r)}n&&m(s,n)},callBackFilter:(t,s,n)=>{if(!n)return;const a=n.querySelector(`.${r}`);return{before:()=>{window.history.pushState("","",o(u,t)),window.localStorage.setItem("lp_filter_courses",JSON.stringify(t)),a&&(a.style.display="block")},success:e=>{n.querySelectorAll(`:not(.${r})`).forEach((e=>{e.closest(`.${r}`)||e.remove()})),n.insertAdjacentHTML("afterbegin",e.data.content||"");const t=document.querySelector(".learn-press-pagination");t&&t.remove();const s=e.data.pagination||"";n.insertAdjacentHTML("afterend",s);const o=document.querySelector(`.${i}`);o&&(o.innerHTML=e.data.from_to||"")},error:e=>{n.innerHTML+=`<div class="lp-ajax-message error" style="display:block">${e.message||"Error"}</div>`},completed:()=>{a&&(a.style.display="none"),n.closest(`.${e}`).scrollIntoView({behavior:"smooth"})}}},callBackPaginationTypeLoadMore:(e,t)=>{if(!t||!e)return!1;const s=e.querySelector(".courses-btn-load-more");let o;return s&&(o=s.querySelector(".lp-loading-circle")),{before:()=>{s&&(o.classList.remove("hide"),s.setAttribute("disabled","disabled"))},success:e=>{t.insertAdjacentHTML("beforeend",e.data.content||""),t.insertAdjacentHTML("afterend",e.data.pagination||"");const s=document.querySelector(`.${i}`);s&&(s.innerHTML=e.data.from_to||"")},error:e=>{t.innerHTML+=`<div class="lp-ajax-message error" style="display:block">${e.message||"Error"}</div>`},completed:()=>{s&&(o.classList.add("hide"),s.remove())}}},callBackPaginationTypeInfinite:(e,t)=>{if(!t||!t)return;const s=e.querySelector(".courses-load-infinite");if(!s)return;const o=s.querySelector(".lp-loading-circle");return w=!0,s.classList.remove("courses-load-infinite"),{before:()=>{o.classList.remove("hide")},success:e=>{t.insertAdjacentHTML("beforeend",e.data.content||""),e.data.pagination&&t.insertAdjacentHTML("afterend",e.data.pagination||"");const s=document.querySelector(`.${i}`);s&&(s.innerHTML=e.data.from_to||"")},error:e=>{t.innerHTML+=`<div class="lp-ajax-message error" style="display:block">${e.message||"Error"}</div>`},completed:()=>{s.remove(),w=!1}}},searchCourse:(s,o)=>{if("c_search"===o.name){s.preventDefault();const e=o.closest("form.search-courses");if(!e)return;return void e.querySelector('button[type="submit"]').click()}if(!o.classList.contains("search-courses"))return;const r=o;s.preventDefault();const n=r.closest(`.${e}`);if(!n)return;if(!n.querySelector(`.${t}`))return;const i=r.querySelector("input[name=c_search]").value;(!i||i&&i.length>2)&&(void 0!==g&&clearTimeout(g),g=setTimeout((function(){f="filter",d.c_search=i,d.paged=1,window.lpCourseList.triggerFetchAPI(d)}),800))},ajaxEnableLoadPage:()=>{let s=0;if(!c){let o;const n={success:n=>{o=setInterval((function(){const a=document.querySelector(`.${r}`),c=document.querySelector(`.${e}`);let l;if(c&&(l=c.querySelector(`.${t}`)),++s,s>5e3&&clearInterval(o),l&&a){clearInterval(o),l.insertAdjacentHTML("afterbegin",n.data.content||""),a.style.display="none";const e=n.data.pagination||"";l.insertAdjacentHTML("afterend",e);const t=document.querySelector(`.${i}`);t&&(t.innerHTML=n.data.from_to||"")}}),1)}};"number"!==p&&(d.paged=1),m(d,n)}},getFilterParams:()=>d,checkIsNewListCourses:()=>!!document.querySelector(".lp-list-courses-default")}})(),document.addEventListener("DOMContentLoaded",(function(){window.lpCourseList.checkIsNewListCourses()||(window.lpCourseList.init(),window.lpCourseList.ajaxEnableLoadPage())}))})();