(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),r=r=>{const{attributes:s,setAttributes:n,context:i}=r,a=(0,t.useBlockProps)(),{lpCourseData:o}=i,l=o?.description||'<h3>Description</h3> <div className="lp-course-description"> <p>Ullo fecit epicurus necesse manilium plebiscito intrandum facto sequamur habemus nostrane adipiscing vocatur poterit caeleste</p> <p>Beatus neget maximarum superiores dacere veriusque isto anquam congressu reprehendi</p></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...a,dangerouslySetInnerHTML:{__html:l}}))},s=e=>null,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-description","title":"Course Description","category":"learnpress-course-elements","description":"Renders template Course Description PHP templates.","textdomain":"learnpress","keywords":["description single course","learnpress"],"ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"heading":true,"gradients":false,"__experimentalDefaultControls":{"text":true,"h3":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),i=window.wp.blocks,a=window.wp.data;let o=null;const l=window.wp.primitives,p=window.ReactJSXRuntime,u=(0,p.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(l.Path,{d:"M4 6h12V4.5H4V6Zm16 4.5H4V9h16v1.5ZM4 15h16v-1.5H4V15Zm0 4.5h16V18H4v1.5Z"})});var c,m,d;c=["learnpress/learnpress//single-lp_course"],m=n,d=e=>{(0,i.registerBlockType)(e.name,{...e,icon:u,edit:r,save:s})},(0,a.subscribe)((()=>{const e={...m},t=(0,a.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const r=t.getCurrentPostId();null!==r&&o!==r&&(o=r,(0,i.getBlockType)(e.name)&&((0,i.unregisterBlockType)(e.name),c.includes(r)?(e.ancestor=null,d(e)):(e.ancestor||(e.ancestor=[]),d(e))))})),(0,i.registerBlockType)(n.name,{...n,icon:u,edit:r,save:s})})();