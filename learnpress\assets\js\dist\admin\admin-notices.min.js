(()=>{"use strict";const e={};let n;"undefined"!=typeof lpDataAdmin&&(n=lpDataAdmin.lp_rest_url,e.admin={apiAdminNotice:n+"lp/v1/admin/tools/admin-notices",apiAdminOrderStatic:n+"lp/v1/orders/statistic",apiAddons:n+"lp/v1/addon/all",apiAddonAction:n+"lp/v1/addon/action-n",apiAddonsPurchase:n+"lp/v1/addon/info-addons-purchase",apiSearchCourses:n+"lp/v1/admin/tools/search-course",apiSearchUsers:n+"lp/v1/admin/tools/search-user",apiAssignUserCourse:n+"lp/v1/admin/tools/assign-user-course",apiUnAssignUserCourse:n+"lp/v1/admin/tools/unassign-user-course"}),"undefined"!=typeof lpData&&(n=lpData.lp_rest_url,e.frontend={apiWidgets:n+"lp/v1/widgets/api",apiCourses:n+"lp/v1/courses/archive-course",apiAJAX:n+"lp/v1/load_content_via_ajax/",apiProfileCoverImage:n+"lp/v1/profile/cover-image"}),n&&(e.apiCourses=n+"lp/v1/courses/");const t=e;let s=null,o=null;const a=window.location.search,i=new URLSearchParams(a).get("tab"),r=(e="")=>{if(!lpGlobalSettings.is_admin)return;let n=i?`?tab=${i}`:"";n+=e?(i?"&":"?")+`${e}`:"",fetch(t.admin.apiAdminNotice+n,{method:"POST",headers:{"X-WP-Nonce":lpGlobalSettings.nonce}}).then((e=>e.json())).then((e=>{const{status:n,message:t,data:a}=e;"success"===n?"Dismissed!"!==t&&(o=a.content,0===o.length?s.style.display="none":(s.innerHTML=o,s.style.display="block",(()=>{try{const e=document.querySelector("#adminmenu");if(!e)return;const n=e.querySelector("#toplevel_page_learn_press");if(!n)return;const t=n.querySelector(".wp-menu-name");if(!t)return;const s=document.querySelector("input[name=lp-addons-new-version-totals]");if(!s)return;const o='<span class="tab-lp-admin-notice"></span>';t.insertAdjacentHTML("beforeend",o);const a=n.querySelector('a[href="admin.php?page=learn-press-addons"]');if(!a)return;const i=`<span style="margin-left: 5px" class="update-plugins">${s.value}</span>`;a.setAttribute("href","admin.php?page=learn-press-addons&tab=update"),a.insertAdjacentHTML("beforeend",i)}catch(e){console.log(e)}})())):o=t})).catch((e=>{console.log(e)}))};((e,n)=>{const t=document.querySelector(e);if(t)return void n();const s=new MutationObserver(((t,s)=>{const o=document.querySelector(e);o&&(s.disconnect(),n())}));s.observe(document.documentElement,{childList:!0,subtree:!0})})(".lp-admin-notices",(()=>{s=document.querySelector(".lp-admin-notices"),r()})),document.addEventListener("click",(e=>{const n=e.target;if(n.classList.contains("btn-lp-notice-dismiss")&&(e.preventDefault(),confirm("Are you sure you want to dismiss this notice?"))){const e=n.closest(".lp-notice");r(`dismiss=${n.getAttribute("data-dismiss")}`),e.remove(),0===s.querySelectorAll(".lp-notice").length&&(s.style.display="none")}}))})();