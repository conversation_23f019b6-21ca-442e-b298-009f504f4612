(()=>{"use strict";const e=e=>{if("string"!=typeof e)return e;const t=String.raw({raw:e}).match(/<-- LP_AJAX_START -->(.*)<-- LP_AJAX_END -->/s);try{e=t?JSON.parse(t[1].replace(/(?:\r\n|\r|\n)/g,"")):JSON.parse(e)}catch(t){e={}}return e},t=jQuery,a=t(document),s=()=>{t(".lp-metabox__custom-fields").on("click",".lp-metabox-custom-field-button",(function(){const a=t(this).data("row").replace(/lp_metabox_custom_fields_key/gi,Math.floor(1e3*Math.random())+1);return t(this).closest("table").find("tbody").append(a),e(t(this).closest(".lp-metabox__custom-fields")),!1})),t(".lp-metabox__custom-fields").on("click","a.delete",(function(){return t(this).closest("tr").remove(),e(t(this).closest(".lp-metabox__custom-fields")),!1})),t(".lp-metabox__custom-fields tbody").sortable({items:"tr",cursor:"move",axis:"y",handle:"td.sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,update(a,s){e(t(this).closest(".lp-metabox__custom-fields"))}});const e=e=>{e.find("tbody tr").each((function(e,a){t(this).find(".sort .count").val(e)}))}},l=()=>{const e=e=>{e.find(".lp_repeater_meta_box__field").each((function(e,a){t(this).find(".lp_repeater_meta_box__field__count").val(e),t(this).find(".lp_repeater_meta_box__title__title > span").text(e+1)}))};t(".lp_repeater_meta_box__add").on("click",(function(){const a=t(this).data("add").replace(/lp_metabox_repeater_key/gi,Math.floor(1e3*Math.random())+1);return t(this).closest(".lp_repeater_meta_box__wrapper").find(".lp_repeater_meta_box__fields").append(a),e(t(this).closest(".lp_repeater_meta_box__wrapper")),t(this).closest(".lp_repeater_meta_box__wrapper").find(".lp_repeater_meta_box__fields").last().find("input").trigger("focus"),!1})),t(".lp_repeater_meta_box__wrapper").on("click","a.lp_repeater_meta_box__title__delete",(function(){return t(this).closest(".lp_repeater_meta_box__field").remove(),e(t(this).closest(".lp_repeater_meta_box__wrapper")),!1})),t(".lp_repeater_meta_box__fields").on("click",".lp_repeater_meta_box__title__toggle, .lp_repeater_meta_box__title__title",(function(){const e=t(this).closest(".lp_repeater_meta_box__field");return e.hasClass("lp_repeater_meta_box__field_active")?e.removeClass("lp_repeater_meta_box__field_active"):e.addClass("lp_repeater_meta_box__field_active"),!1})),t(".lp_repeater_meta_box__fields").sortable({items:".lp_repeater_meta_box__field",cursor:"grab",axis:"y",handle:".lp_repeater_meta_box__title__sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,update(a,s){e(t(this).closest(".lp_repeater_meta_box__wrapper"))}})},i=()=>{t(".lp_course_extra_meta_box__add").on("click",(function(){return t(this).closest(".lp_course_extra_meta_box__content").find(".lp_course_extra_meta_box__fields").append(t(this).data("add")),t(this).closest(".lp_course_extra_meta_box__content").find(".lp_course_extra_meta_box__field").last().find("input").trigger("focus"),!1})),t(".lp_course_extra_meta_box__fields").on("click","a.delete",(function(){return t(this).closest(".lp_course_extra_meta_box__field").remove(),!1})),t(".lp_course_extra_meta_box__fields").sortable({items:".lp_course_extra_meta_box__field",cursor:"grab",axis:"y",handle:".sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65}),t(".lp_course_faq_meta_box__add").on("click",(function(){return t(this).closest(".lp_course_faq_meta_box__content").find(".lp_course_faq_meta_box__fields").append(t(this).data("add")),!1})),t(".lp_course_faq_meta_box__fields").on("click","a.delete",(function(){return t(this).closest(".lp_course_faq_meta_box__field").remove(),!1})),t(".lp_course_faq_meta_box__fields").sortable({items:".lp_course_faq_meta_box__field",cursor:"grab",axis:"y",handle:".sort",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65})},o=()=>{t(".lp-metabox__colorpick").iris({change(e,a){t(this).parent().find(".colorpickpreview").css({backgroundColor:a.color.toString()})},hide:!0,border:!0}).on("click focus",(function(e){e.stopPropagation(),t(".iris-picker").hide(),t(this).closest("td").find(".iris-picker").show(),t(this).data("original-value",t(this).val())})).on("change",(function(){t(this).is(".iris-error")&&(t(this).data("original-value").match(/^\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/)?t(this).val(t(this).data("original-value")).trigger("change"):t(this).val("").trigger("change"))})),t("body").on("click",(function(){t(".iris-picker").hide()}))},n=()=>{t(".lp-metabox-field__image-advanced").each(((e,a)=>{let s;const l=t(a).find("#lp-gallery-images-ids"),i=t(a).find(".lp-metabox-field__image-advanced-images"),o=t(a).find(".lp-metabox-field__image-advanced-upload > a");t(o).on("click",(e=>{e.preventDefault(),s||(s=wp.media({title:o.data("choose"),button:{text:o.data("update")},states:[new wp.media.controller.Library({title:o.data("choose"),filterable:"all",multiple:!0})]}),s.on("select",(function(){const e=s.state().get("selection");let t=l.val();e.forEach((function(e){if((e=e.toJSON()).id){t=t?t+","+e.id:e.id;const a=e.sizes&&e.sizes.thumbnail?e.sizes.thumbnail.url:e.url;i.append('<li class="image" data-attachment_id="'+e.id+'"><img src="'+a+'" /><ul class="actions"><li><a href="#" class="delete" title="'+o.data("delete")+'">'+o.data("text")+"</a></li></ul></li>")}})),l.val(t)}))),s.open()})),i.sortable({items:"li.image",cursor:"move",scrollSensitivity:40,forcePlaceholderSize:!0,forceHelperSize:!1,helper:"clone",opacity:.65,placeholder:"lp-metabox-sortable-placeholder",start(e,t){t.item.css("background-color","#f6f6f6")},stop(e,t){t.item.removeAttr("style")},update(){let e="";i.find("li.image").css("cursor","default").each((function(){const a=t(this).attr("data-attachment_id");e=e+a+","})),l.val(e)}}),t(i).find("li.image").each(((e,a)=>{t(a).find("a.delete").on("click",(()=>{t(a).remove();let e="";return t(i).find("li.image").css("cursor","default").each((function(){const a=t(this).attr("data-attachment_id");e=e+a+","})),l.val(e),!1}))}))}))},r=()=>{t(document.body).on("lp-metabox-course-tab-panels",(function(){t("ul.lp-meta-box__course-tab__tabs").show(),t("ul.lp-meta-box__course-tab__tabs a").on("click",(function(e){e.preventDefault();const a=t(this).closest("div.lp-meta-box__course-tab");t("ul.lp-meta-box__course-tab__tabs li",a).removeClass("active"),t(this).parent().addClass("active"),t("div.lp-meta-box-course-panels",a).hide(),t(t(this).attr("href")).show()})),t("div.lp-meta-box__course-tab").each((function(){t(this).find("ul.lp-meta-box__course-tab__tabs li").eq(0).find("a").trigger("click")}))})).trigger("lp-metabox-course-tab-panels")},c=function(a){a.preventDefault();const s=t(this).closest("tr"),l=(t(this),s.find(".status").hasClass("enabled")?"no":"yes");t.ajax({url:"",data:{"lp-ajax":"update-payment-status",status:l,id:s.data("payment"),nonce:t("input[name=lp-settings-nonce]").val()},success(a){a=e(a);for(const e in a)t("#payment-"+e+" .status").toggleClass("enabled",a[e])}})},_=function(){(function(){t.post({url:window.location.href,data:{"lp-ajax":"update_email_status",status:t(this).parent().hasClass("enabled")?"no":"yes",id:t(this).data("id"),nonce:t("input[name=lp-settings-nonce]").val()},dataType:"text",success:t.proxy((function(a){a=e(a);for(const e in a)t("#email-"+e+" .status").toggleClass("enabled",a[e])}),this)})}).apply(this)},d=()=>{t("#course-settings").length&&(t(".lp_sale_dates_fields").each((function(){const e=t(this).closest("#price_course_data");let a=!1;t(this).find("input").each((function(){""!==t(this).val()&&(a=!0)})),a?(e.find(".lp_sale_price_schedule").hide(),e.find(".lp_sale_dates_fields").show()):(e.find(".lp_sale_price_schedule").show(),e.find(".lp_sale_dates_fields").hide())})),t(".lp-meta-box-course-panels").on("click",".lp_sale_price_schedule",(function(){const e=t(this).closest("#price_course_data");return t(this).hide(),e.find(".lp_cancel_sale_schedule").show(),e.find(".lp_sale_dates_fields").show(),!1})),t(".lp-meta-box-course-panels").on("click",".lp_cancel_sale_schedule",(function(){const e=t(this).closest("div.lp-meta-box-course-panels");return t(this).hide(),e.find(".lp_sale_price_schedule").show(),e.find(".lp_sale_dates_fields").hide(),e.find(".lp_sale_dates_fields").find("input").val(""),!1})),t(document).on("input","#price_course_data",(function(e){const a=t(this),s=t(".lp_meta_box_regular_price"),l=t(".lp_meta_box_sale_price"),i=t(e.target).attr("id");a.find(".learn-press-tip-floating").remove(),parseInt(l.val())>parseInt(s.val())&&("_lp_price"===i?s.parent(".form-field").append('<div class="learn-press-tip-floating">'+lpAdminCourseEditorSettings.i18n.notice_price+"</div>"):"_lp_sale_price"===i&&l.parent(".form-field").append('<div class="learn-press-tip-floating">'+lpAdminCourseEditorSettings.i18n.notice_sale_price+"</div>"))})))},p=function(){const e=t(this);if(e.hasClass("current"))return!1;const a=t("#learn-press-template-files"),s=a.find("tr[data-template]"),l=e.data("template"),i=e.data("filter");return e.addClass("current").siblings("a").removeClass("current"),l?s.map((function(){t(this).toggleClass("hide-if-js",t(this).data("template")!==l)})):i?s.map((function(){t(this).toggleClass("hide-if-js","yes"!==t(this).data("filter-"+i))})):s.removeClass("hide-if-js"),t("#learn-press-no-templates").toggleClass("hide-if-js",!!a.find("tr.template-row:not(.hide-if-js):first").length),!1},u=function(a){a.preventDefault();const s=t(this).data("status");t.ajax({url:"",data:{"lp-ajax":"update_email_status",nonce:t("input[name=lp-settings-nonce]").val(),status:s},success(a){a=e(a);for(const e in a)t("#email-"+e+" .status").toggleClass("enabled",a[e])}})};t(document).ready((function(){t(".learn-press-payments.sortable tbody").sortable({handle:".dashicons-menu",helper:(e,a)=>(a.children().each((function(){t(this).width(t(this).width())})),a),axis:"y",start(e,t){},stop(e,t){},update(e,a){const s=t(this).children().map((function(){return t(this).find('input[name="payment-order"]').val()})).get();t.post({url:"",data:{"lp-ajax":"update-payment-order",order:s,nonce:t("input[name=lp-settings-nonce]").val()},success(e){}})}}),function(){if(t.fn.select2){const e=t(".lp-select-2 select");e.select2({placeholder:"Select a value"}),e.on("change.select2",(function(e){const a=t(e.target);a.val().length||a.val(null)})),t(".lp_autocomplete_metabox_field").each((function(){const e=t(this).data("atts");let a=e.action;a||(a="users"===e.data?e.rest_url+"wp/v2/users":e.rest_url+"wp/v2/"+e.data),t(this).find("select").select2({placeholder:e.placeholder?e.placeholder:"Select",ajax:{url:a,dataType:"json",delay:250,beforeSend(t){t.setRequestHeader("X-WP-Nonce",e.nonce)},data:e=>({search:e.term}),processResults:e=>({results:e.map((e=>({id:e.id,text:e.title&&e.title.rendered?e.title.rendered:e.name})))}),cache:!0},minimumInputLength:2})}))}}(),t(".learn-press-tooltip").each((function(){const e=t(this),a=t.extend({title:"data-tooltip",offset:10,gravity:"s"},e.data());e.tipsy(a)})),a.on("change",'.learn-press-single-course-permalink input[type="radio"]',(function(){const e=t(this).closest(".learn-press-single-course-permalink");e.hasClass("custom-base")?e.find('input[type="text"]').prop("readonly",!1):e.siblings(".custom-base").find('input[type="text"]').prop("readonly",!0)})).on("change","input.learn-press-course-base",(function(){t("#course_permalink_structure").val(t(this).val())})).on("focus","#course_permalink_structure",(function(){t("#learn_press_custom_permalink").click()})).on("change","#learn_press_courses_page_id",(function(){t("tr.learn-press-courses-page-id").toggleClass("hide-if-js",!parseInt(this.value))})),r(),s(),o(),n(),t(".lp-metabox-field__image").each(((e,a)=>{let s;const l=t(a).find(".lp-metabox-field__image--add"),i=t(a).find(".lp-metabox-field__image--delete"),o=t(a).find(".lp-metabox-field__image--image"),n=t(a).find(".lp-metabox-field__image--id");n.val()?(l.hide(),i.show()):(l.show(),i.hide()),l.on("click",(e=>{e.preventDefault(),s||(s=wp.media({title:l.data("choose"),button:{text:l.data("update")},multiple:!1}),s.on("select",(function(){const e=s.state().get("selection").first().toJSON(),t=e.sizes&&e.sizes.thumbnail?e.sizes.thumbnail.url:e.url;o.append('<div class="lp-metabox-field__image--inner"><img src="'+t+'" alt="" style="max-width:100%;"/></div>'),n.val(e.id),l.hide(),i.show()}))),s.open()})),i.on("click",(e=>{e.preventDefault(),o.html(""),l.show(),i.hide(),n.val("")}))})),d(),i(),(()=>{const e=["evaluate_final_quiz","evaluate_final_assignment"];[...document.querySelectorAll("input[type=radio][name=_lp_course_result]")].map(((a,s)=>(a.checked&&e.includes(a.value)&&t("._lp_passing_condition_field").hide(),null))),t("input[type=radio][name=_lp_course_result]").on("change",(function(a){e.includes(a.target.value)?t("._lp_passing_condition_field").hide():t("._lp_passing_condition_field").show()}))})(),(()=>{[...document.querySelectorAll(".lp-metabox-get-final-quiz")].map((t=>{t.addEventListener("click",(a=>{a.preventDefault();const s=t.textContent,l=t.dataset.loading,i=document.querySelector(".lp-metabox-evaluate-final_quiz");i&&i.remove(),t.textContent=l,e(t).then((e=>{const{message:a,data:l}=e;t.textContent=s;const i=document.createElement("div");i.className="lp-metabox-evaluate-final_quiz",i.innerHTML=l||a,t.parentNode.insertBefore(i,t.nextSibling)}))}))}));const e=async e=>await wp.apiFetch({path:"lp/v1/admin/course/get_final_quiz",method:"POST",data:{courseId:e.dataset.postid||""}})})(),l(),t(document).on("click",".learn-press-payments .status .dashicons",c).on("click",".change-email-status",_).on("click",".learn-press-filter-template",p).on("click","#learn-press-enable-emails, #learn-press-disable-emails",u)})),document.addEventListener("keydown",(function(e){const t=e.target;"Enter"!==e.key&&13!==e.keyCode||(t.classList.contains("lp_course_extra_meta_box__input")||"INPUT"===t.tagName&&t.closest(".lp_course_faq_meta_box__field"))&&(e.preventDefault(),t.blur())}))})();