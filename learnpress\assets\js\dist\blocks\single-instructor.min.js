(()=>{"use strict";const e=window.React,r=window.wp.blockEditor,t=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/single-instructor","title":"Single Instructor","category":"learnpress-category","icon":"archive","description":"Renders Template Single Instructor PHP templates.","textdomain":"learnpress","keywords":["learnpress single instructor","learnpress"],"usesContext":[],"supports":{"inserter":true,"reusable":true,"reorder":true,"html":false,"multiple":true,"wrapper":false}}');(0,window.wp.blocks.registerBlockType)(t.name,{...t,edit:t=>{const n=(0,r.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...n},(0,e.createElement)(r.InnerBlocks,null)))},save:t=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.InnerBlocks.Content,null))})})();