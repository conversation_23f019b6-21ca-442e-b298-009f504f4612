/* Learnpressium Admin Styles */

/* Trainees Table Styles */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
}

.trainees-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 900px; /* Ensures table doesn't shrink too much */
}

.trainees-table th,
.trainees-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.trainees-table th {
    background-color: #f9f9f9;
    font-weight: 700;
    font-size: 16px;
}

/* Additional styling for header cells */
.header-cell {
    font-size: 16px;
    font-weight: 500 !important; /* Force boldness */
    text-transform: uppercase; /* Make headers more distinct */
}

.table-title {
    margin-top: 20px;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 500;
    color: #23282d;
    text-align: left;
}

.trainees-table tr:hover {
    background-color: #f5f5f5;
}

/* Enrolled Column Styles */
.enrollment-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: bold;
}

.enrollment-status.enrolled {
    background-color: #dff0d8;
    color: #3c763d;
}

.enrollment-status.not-enrolled {
    background-color: #f2dede;
    color: #a94442;
}

/* Enrolled Courses Column Styles */
.enrolled-courses-list {
    margin: 0;
    padding-left: 20px;
}

.enrolled-courses-list li {
    margin-bottom: 4px;
}

.no-courses {
    color: #999;
    font-style: italic;
}

.column-enrolled-courses,
.column-active-enrolled-courses {
    min-width: 200px;
}

.column-active-enrolled {
    min-width: 100px;
}

/* Set ID column to be narrower */
.column-id {
    width: 50px;
    max-width: 50px;
}

/* Download button styles */
.trainees-actions {
    margin: 15px 0;
}

#export-trainees {
    display: inline-flex;
    align-items: center;
}

/* Hover effect for trainee rows */
.trainee-name-container {
    position: relative;
}

.row-actions {
    position: absolute;
    left: -9999px;
    color: #666;
    font-size: 13px;
}

.trainee-row:hover .row-actions {
    position: static;
    left: 0;
    display: inline-block;
    padding-left: 10px;
}

.row-actions .view a {
    color: #0073aa;
    text-decoration: none;
}

.row-actions .view a:hover {
    color: #00a0d2;
    text-decoration: underline;
}
