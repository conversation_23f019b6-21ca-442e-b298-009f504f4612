/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/edit.js":
/*!******************************************************************************!*\
  !*** ./assets/src/apps/js/blocks/single-course-item/item-curriculum/edit.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_block_editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/block-editor */ "@wordpress/block-editor");
/* harmony import */ var _wordpress_block_editor__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_block_editor__WEBPACK_IMPORTED_MODULE_3__);




const Edit = props => {
  const blockProps = (0,_wordpress_block_editor__WEBPACK_IMPORTED_MODULE_3__.useBlockProps)();
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    ...blockProps
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "lp-course-curriculum"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-curriculum"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    class: "course-sections"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-section"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-down"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-up"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section__title"
  }, "Section 1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-count-items"
  }, "10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    class: "course-section__items"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "763",
    "data-item-order": "1",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 1"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "764",
    "data-item-order": "2",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 2"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "765",
    "data-item-order": "3",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 3"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "766",
    "data-item-order": "4",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 4"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "767",
    "data-item-order": "5",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.5")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 5"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "768",
    "data-item-order": "6",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.6")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 6"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "769",
    "data-item-order": "7",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: "http://lp.local/courses/zab/lessons/lesson-7-5/",
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.7")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 7"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "770",
    "data-item-order": "8",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: "http://lp.local/courses/zab/lessons/lesson-8-5/",
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.8")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 8"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "771",
    "data-item-order": "9",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: "http://lp.local/courses/zab/lessons/lesson-9-5/",
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.9")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 9"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "772",
    "data-item-order": "10",
    "data-item-type": "lp_quiz"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_quiz"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "1.10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Quiz 1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__right"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "duration"
  }, "40 Minutes"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "question-count"
  }, "15 Questions"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-section lp-collapse"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-down"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-up"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section__title"
  }, "Section 2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-count-items"
  }, "14")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    class: "course-section__items"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "788",
    "data-item-order": "1",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 10"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "789",
    "data-item-order": "2",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 11"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "790",
    "data-item-order": "3",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 12"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "791",
    "data-item-order": "4",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 13"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "792",
    "data-item-order": "5",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.5")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 14"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "793",
    "data-item-order": "6",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.6")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 15"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "794",
    "data-item-order": "7",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.7")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 16"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "795",
    "data-item-order": "8",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.8")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 17"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "796",
    "data-item-order": "9",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.9")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 18"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "797",
    "data-item-order": "10",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 19"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "798",
    "data-item-order": "11",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.11")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 20"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "799",
    "data-item-order": "12",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.12")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 21"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "800",
    "data-item-order": "13",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.13")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 22"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "801",
    "data-item-order": "14",
    "data-item-type": "lp_quiz"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_quiz"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "2.14")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Quiz 2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__right"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "duration"
  }, "40 Minutes"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "question-count"
  }, "15 Questions"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-section lp-collapse"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-down"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-up"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section__title"
  }, "Section 3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-count-items"
  }, "10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    class: "course-section__items"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "817",
    "data-item-order": "1",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 23"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "818",
    "data-item-order": "2",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 24"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "819",
    "data-item-order": "3",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 25"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "820",
    "data-item-order": "4",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 26"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "821",
    "data-item-order": "5",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.5")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 27"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "822",
    "data-item-order": "6",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.6")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 28"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "823",
    "data-item-order": "7",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.7")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 29"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "824",
    "data-item-order": "8",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.8")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 30"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "825",
    "data-item-order": "9",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.9")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 31"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "826",
    "data-item-order": "10",
    "data-item-type": "lp_quiz"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_quiz"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "3.10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Quiz 3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__right"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "duration"
  }, "30 Minutes"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "question-count"
  }, "10 Questions"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-section lp-collapse"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-down"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    class: "lp-icon-angle-up"
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section-info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-section__title"
  }, "Section 4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "section-count-items"
  }, "13")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    class: "course-section__items"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "837",
    "data-item-order": "1",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.1")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 32"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "838",
    "data-item-order": "2",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.2")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 33"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "839",
    "data-item-order": "3",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.3")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 34"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "840",
    "data-item-order": "4",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 35"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "841",
    "data-item-order": "5",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.5")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 36"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "842",
    "data-item-order": "6",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.6")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 37"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "843",
    "data-item-order": "7",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.7")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 38"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "844",
    "data-item-order": "8",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.8")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 39"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "845",
    "data-item-order": "9",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.9")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 40"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "846",
    "data-item-order": "10",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.10")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 41"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "847",
    "data-item-order": "11",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.11")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 42"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "848",
    "data-item-order": "12",
    "data-item-type": "lp_lesson"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_lesson"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.12")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Lesson 43"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  })))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    class: "course-item ",
    "data-item-id": "849",
    "data-item-order": "13",
    "data-item-type": "lp_quiz"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    class: "course-item__link"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__info"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico lp_quiz"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-order lp-hidden"
  }, "4.13")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__content"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__left"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item-title"
  }, "Quiz 4")), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__right"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "duration"
  }, "10 Minutes"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "question-count"
  }, "10 Questions"))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    class: "course-item__status"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    class: "course-item-ico in-progress"
  }))))))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Edit);

/***/ }),

/***/ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/save.js":
/*!******************************************************************************!*\
  !*** ./assets/src/apps/js/blocks/single-course-item/item-curriculum/save.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   save: () => (/* binding */ save)
/* harmony export */ });
const save = props => null;

/***/ }),

/***/ "./assets/src/apps/js/blocks/utilBlock.js":
/*!************************************************!*\
  !*** ./assets/src/apps/js/blocks/utilBlock.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   checkTemplatesCanLoadBlock: () => (/* binding */ checkTemplatesCanLoadBlock)
/* harmony export */ });
/* harmony import */ var _wordpress_blocks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/blocks */ "@wordpress/blocks");
/* harmony import */ var _wordpress_blocks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_blocks__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/data */ "@wordpress/data");
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_1__);
/**
 * Check if the block can be loaded in the current template.
 *
 * @since 4.2.8.4
 * @version 1.0.0
 */


let currentPostIdOld = null;
const checkTemplatesCanLoadBlock = (templates, metadata, callBack) => {
  (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_1__.subscribe)(() => {
    const metaDataNew = {
      ...metadata
    };
    const store = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_1__.select)('core/editor') || null;
    if (!store || typeof store.getCurrentPostId !== 'function' || !store.getCurrentPostId()) {
      return;
    }
    const currentPostId = store.getCurrentPostId();
    if (currentPostId === null) {
      return;
    }
    if (currentPostIdOld === currentPostId) {
      return;
    }
    currentPostIdOld = currentPostId;
    if ((0,_wordpress_blocks__WEBPACK_IMPORTED_MODULE_0__.getBlockType)(metaDataNew.name)) {
      (0,_wordpress_blocks__WEBPACK_IMPORTED_MODULE_0__.unregisterBlockType)(metaDataNew.name);
      if (templates.includes(currentPostId)) {
        metaDataNew.ancestor = null;
        callBack(metaDataNew);
      } else {
        if (!metaDataNew.ancestor) {
          metaDataNew.ancestor = [];
        }
        callBack(metaDataNew);
      }
    }
  });
};


/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/***/ ((module) => {

module.exports = window["React"];

/***/ }),

/***/ "@wordpress/block-editor":
/*!*************************************!*\
  !*** external ["wp","blockEditor"] ***!
  \*************************************/
/***/ ((module) => {

module.exports = window["wp"]["blockEditor"];

/***/ }),

/***/ "@wordpress/blocks":
/*!********************************!*\
  !*** external ["wp","blocks"] ***!
  \********************************/
/***/ ((module) => {

module.exports = window["wp"]["blocks"];

/***/ }),

/***/ "@wordpress/components":
/*!************************************!*\
  !*** external ["wp","components"] ***!
  \************************************/
/***/ ((module) => {

module.exports = window["wp"]["components"];

/***/ }),

/***/ "@wordpress/data":
/*!******************************!*\
  !*** external ["wp","data"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["data"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ }),

/***/ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json":
/*!*********************************************************************************!*\
  !*** ./assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json ***!
  \*********************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/item-curriculum","title":"Item Curriculum","category":"learnpress-category","icon":"list-view","description":"Renders template Single Course Legacy PHP templates.","textdomain":"learnpress","keywords":["item curriculum course","learnpress"],"usesContext":[],"supports":{"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}');

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
/*!*******************************************************************************!*\
  !*** ./assets/src/apps/js/blocks/single-course-item/item-curriculum/index.js ***!
  \*******************************************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit */ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/edit.js");
/* harmony import */ var _save__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./save */ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/save.js");
/* harmony import */ var _block_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./block.json */ "./assets/src/apps/js/blocks/single-course-item/item-curriculum/block.json");
/* harmony import */ var _wordpress_blocks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/blocks */ "@wordpress/blocks");
/* harmony import */ var _wordpress_blocks__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_blocks__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _utilBlock_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utilBlock.js */ "./assets/src/apps/js/blocks/utilBlock.js");
/**
 * Register block item curriculum.
 */






const templatesName = ['learnpress/learnpress//single-lp_course_item'];
(0,_utilBlock_js__WEBPACK_IMPORTED_MODULE_4__.checkTemplatesCanLoadBlock)(templatesName, _block_json__WEBPACK_IMPORTED_MODULE_2__, metadataNew => {
  (0,_wordpress_blocks__WEBPACK_IMPORTED_MODULE_3__.registerBlockType)(metadataNew.name, {
    ...metadataNew,
    edit: _edit__WEBPACK_IMPORTED_MODULE_0__["default"],
    save: _save__WEBPACK_IMPORTED_MODULE_1__.save
  });
});
(0,_wordpress_blocks__WEBPACK_IMPORTED_MODULE_3__.registerBlockType)(_block_json__WEBPACK_IMPORTED_MODULE_2__.name, {
  ..._block_json__WEBPACK_IMPORTED_MODULE_2__,
  edit: _edit__WEBPACK_IMPORTED_MODULE_0__["default"],
  save: _save__WEBPACK_IMPORTED_MODULE_1__.save
});
/******/ })()
;
//# sourceMappingURL=item-curriculum.js.map