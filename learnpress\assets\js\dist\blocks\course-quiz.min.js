(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wp.blockEditor,l=window.wp.components,r=(window.wp.element,r=>{const s=(0,n.useBlockProps)(),{attributes:o,setAttributes:a,context:i}=r,{lpCourseData:c}=i,u=c?.quiz||'<div class="course-count-quiz"><div class="course-count-item lp_quiz">9 Quizzes</div></div>';return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(l.<PERSON>,{title:(0,t.__)("Settings","learnpress")},(0,e.createElement)(l.<PERSON><PERSON>ont<PERSON>,{label:(0,t.__)("Show Label","learnpress"),checked:o.showLabel,onChange:e=>{a({showLabel:e})}}),(0,e.createElement)(l.<PERSON>,{label:(0,t.__)("Show Icon","learnpress"),checked:o.showIcon,onChange:e=>{a({showIcon:e})}}))),(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"info-meta-item"},(0,e.createElement)("span",{className:"info-meta-left"},o.showIcon&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:'<i class="lp-icon-puzzle-piece"></i>'}}),o.showLabel?"Quiz:":""),(0,e.createElement)("span",{className:"info-meta-right",dangerouslySetInnerHTML:{__html:u}}))))}),s=e=>null,o=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-quiz","title":"Course Quiz Count","category":"learnpress-course-elements","description":"Show number quiz of course.","textdomain":"learnpress","keywords":["quiz","learnpress"],"icon":"editor-help","ancestor":["learnpress/single-course","learnpress/course-item-template"],"usesContext":["lpCourseData"],"attributes":{"showIcon":{"type":"boolean","default":true},"showLabel":{"type":"boolean","default":true}},"supports":{"multiple":true,"align":["wide","full"],"html":false,"typography":{"fontSize":true,"lineHeight":false,"fontWeight":true,"__experimentalFontFamily":false,"__experimentalTextDecoration":false,"__experimentalFontStyle":false,"__experimentalFontWeight":true,"__experimentalLetterSpacing":false,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}},"color":{"background":false,"text":true,"link":false,"gradients":false,"__experimentalDefaultControls":{"text":true}},"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}'),a=window.wp.blocks,i=window.wp.data;let c=null;var u,p,m;u=["learnpress/learnpress//single-lp_course"],p=o,m=e=>{(0,a.registerBlockType)(e.name,{...e,edit:r,save:s})},(0,i.subscribe)((()=>{const e={...p},t=(0,i.select)("core/editor")||null;if(!t||"function"!=typeof t.getCurrentPostId||!t.getCurrentPostId())return;const n=t.getCurrentPostId();null!==n&&c!==n&&(c=n,(0,a.getBlockType)(e.name)&&((0,a.unregisterBlockType)(e.name),u.includes(n)?(e.ancestor=null,m(e)):(e.ancestor||(e.ancestor=[]),m(e))))})),(0,a.registerBlockType)(o.name,{...o,edit:r,save:s})})();