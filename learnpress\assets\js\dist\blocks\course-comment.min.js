(()=>{"use strict";const e=window.React,t=(window.wp.i18n,window.wp.blockEditor),n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"learnpress/course-comment","title":"Course Comment","category":"learnpress-course-elements","icon":"format-chat","description":"Renders Comment PHP Template.","textdomain":"learnpress","keywords":["course comment","learnpress"],"usesContext":[],"supports":{"multiple":false,"html":false,"spacing":{"padding":true,"margin":true,"__experimentalDefaultControls":{"margin":false,"padding":false}}}}');(0,window.wp.blocks.registerBlockType)("learnpress/course-comment",{...n,icon:{src:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"wc-block-editor-components-block-icon","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M14 10.1V4c0-.6-.4-1-1-1H5c-.6 0-1 .4-1 1v8.3c0 .*******.*******.******* 0 .5-.1.6-.3l1.8-1.8H13c.6 0 1-.4 1-1zm-1.5-.5H6.7l-1.2 1.2V4.5h7v5.1zM19 12h-8c-.6 0-1 .4-1 1v6.1c0 .6.4 1 1 1h5.7l1.8 1.8c.*******.6.3.1 0 .2 0 .3-.1.4-.1.6-.5.6-.8V13c0-.6-.4-1-1-1zm-.5 7.8l-1.2-1.2h-5.8v-5.1h7v6.3z"}))},edit:n=>{const s=(0,t.useBlockProps)();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{...s},(0,e.createElement)("div",{className:"comment-respond"},(0,e.createElement)("h3",{className:"comment-reply-title"},"Leave a Reply"),(0,e.createElement)("p",{className:"comment-form-comment"},(0,e.createElement)("label",{htmlFor:"comment"},"Comment"," ",(0,e.createElement)("span",{className:"required"},"*")),(0,e.createElement)("textarea",{name:"comment"})),(0,e.createElement)("div",{className:"form-submit wp-block-button"},(0,e.createElement)("input",{name:"submit",type:"submit",className:"submit wp-block-button__link wp-element-button",value:"Post Comment"})))))},save:e=>null})})();