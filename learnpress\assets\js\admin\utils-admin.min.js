import*as Utils from"../utils.js";import TomSelect from"tom-select";import Api from"../api.js";const AdminUtilsFunctions={buildTomSelect(t,e,n,o,s){if(!t)return;const i={plugins:{remove_button:{title:"Remove this item"}},onInitialize(){},onItemAdd(e){if(n){const i=Array.from(t.selectedOptions).map((t=>t.value));i.push(e),o.id_not_in=i.join(","),n("",o,s)}}};n&&(i.load=(e,i)=>{const c=Array.from(t.selectedOptions).map((t=>t.value));o.id_not_in=c.join(","),n(e,o,AdminUtilsFunctions.callBackTomSelectSearchAPI(i,s))});const c=(e={...i,...e}).options;if(e?.options?.length>20){const n=20,o=e.options.length;let s=0;const i={...e};i.options=c.slice(s,n);const l=new TomSelect(t,i);s+=n;const a=setInterval((()=>{s>o-1&&clearInterval(a);const t=c.slice(s,s+n);s+=n,l.addOptions(t),l.setValue(e.items)}),200);return l}return new TomSelect(t,e)},callBackTomSelectSearchAPI:(t,e)=>({success:n=>{const o=e.success(n);t(o)}}),fetchCourses(t="",e={},n){const o=Api.admin.apiSearchCourses;e.search=t;const s={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(e)};Utils.lpFetchAPI(o,s,n)},fetchUsers(t="",e={},n){const o=Api.admin.apiSearchUsers;e.search=t;const s={headers:{"Content-Type":"application/json","X-WP-Nonce":lpDataAdmin.nonce},method:"POST",body:JSON.stringify(e)};Utils.lpFetchAPI(o,s,n)}};export{Utils,AdminUtilsFunctions,Api};